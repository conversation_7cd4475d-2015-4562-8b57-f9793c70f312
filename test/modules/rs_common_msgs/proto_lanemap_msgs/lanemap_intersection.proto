syntax = "proto2";  

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";

message Connection {
  optional uint32 lane_id = 1;
  repeated uint32 predecessor_id = 2;
  repeated uint32 successor_id = 3;

  enum LinkTurn {
    UNKNOWN = 0;
    LEFT = 1;
    RIGHT = 2;
    STRAIGHT = 3;
    TURN_AROUND = 4;
  };
  repeated LinkTurn turn_type = 4;
}

message Intersection {
  optional uint32 id = 1;

  optional Polygon polygon = 2;

  enum Type {
    UNKNOWN = 0;
    CROSS_ROAD = 1;
  };
  optional Type type = 3;

  repeated Connection connections = 4;

  optional bool topo_filtered = 5;

  repeated uint32 road_predecessor_id = 6;
  repeated uint32 road_successor_id = 7;
}
