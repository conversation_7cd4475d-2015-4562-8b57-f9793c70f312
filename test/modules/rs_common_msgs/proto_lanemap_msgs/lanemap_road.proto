syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";



message Road {
  optional uint32 id = 1;
  optional Polygon polygon = 2;
  optional int32 type = 3;
  repeated uint32 predecessor_id = 4;
  repeated uint32 successor_id = 5;
  optional bool topo_filtered = 6 [default = true];



  optional bool navi_tag = 7;
  optional Linesegment polyline = 8;
  repeated uint32 laneset = 9;


  enum RoadType{
    LT_NORMAL               = 0X00000000; // 普通
    LT_TUNNEL               = 0X00000001; // 隧道
    LT_BRIDGE               = 0X00000002; // 桥
    LT_TOLLBOOTH            = 0X00000004; // 收费站道路
    LT_DEAD_END             = 0X00000008; // 断头路
    LT_IC                   = 0X00000010; // IC(高速连接普通路的道路)
    LT_JCT                  = 0X00000020; // JCT(高速连接高速的道路)
    LT_SAPA                 = 0X00000040; // SAPA(服务器、停车区道路)
    LT_WITHIN_INTERSECTION  = 0X00000080; // 路口内道路
    LT_AUTHORIZED           = 0X00000100; // 授权道路
    LT_TOLLGATE             = 0X00000200; // 收费岛道路
    LT_ABANDONED_TOLLGATE   = 0X00000400; // 废弃收费岛道路
    LT_CHECKPOINT           = 0X00000800; // 检查站道路
    LT_ROUNDABOUT           = 0X00001000; // 环岛内道路
    LT_SERRATED             = 0X00002000; // 锯齿道路
    LT_MAIN_ROAD            = 0X00004000; // 主路(main road)
    LT_SIDE_ROAD            = 0X00008000; // 辅路(side road)
    LT_MAINROAD_CONNECTION  = 0X00010000; // 主辅路连接路(mainside connection)
    LT_NO_S_INTERSECTION    = 0X00020000; // 无小路口
    LT_S_INTERSECTION_LEFT  = 0X00040000; // 小路口左侧道路
    LT_S_INTERSECTION_RIGHT = 0X00080000; // 小路口右侧道路
    LT_S_INTERSECTION_BOTH  = 0X00100000; // 小路口两侧道路
    LT_INJUNCTION           = 0X00200000; // 路口内道路（编译计算）
    LF_BOOTH_EXIT           = 0X00400000; // 出口收费站
    LF_BOOTH_ENTRANCE       = 0X00800000; // 入口收费站
    LF_BOOTH_EXIT_ENTRANCE  = 0X01000000; // 出入口收费站
    LT_UNSTRUCTURE_ROAD     = 0X02000000; // 非结构化道路
  };

  enum Direction {
    IN_POSITIVE_DIRECTION       = 1; // 正向
    IN_NEGATIVE_DIRECTION       = 2; // 反向
    IN_BOTH_DIRECTIONS_PROHIBIT = 3; // 双向禁止
    IN_BOTH_DIRECTIONS          = 4; // 双向
  };


  optional uint32 road_type = 10; // use RoadType bits |
  optional Direction direction = 11;

  optional bool cutoff = 12 [default = false];
}
