syntax = "proto2";

package robosense.rs_map.com_msgs.navimsgs;

import "modules/rs_common_msgs/proto_nav_msgs/common.proto";

message Trajectory
{
  required uint64 id = 1;
  repeated PointLLA trajectory = 2;    // 高德返回的轨迹
}


message Trajectorys {
  enum PathType {
    PREDEFINE = 0;  // 预制路线
    NORMAL = 1;     // 高德导航模式
  };
  required PathType path_type = 1;

  optional string predefine_path = 2;   // 用户选择的预制路线, 目前有 shenzhen_demoroad 和 shanghai_demoroad
  repeated Trajectory trajectorys = 3;  // 高德返回的轨迹
  optional uint64 timestamp = 4;
}
