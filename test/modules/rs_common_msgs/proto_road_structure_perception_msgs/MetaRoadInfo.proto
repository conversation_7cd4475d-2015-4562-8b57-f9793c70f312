syntax = "proto2";
package robosense.perception;
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap.proto";
import "modules/common_msgs/localization_msgs/pose.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Lane.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Curb.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/VectorizedLaneLine.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/StopLine.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/RoadStructure.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Intersection.proto";


message Header {
  required uint32 seq = 1;
  required uint64 timestamp = 2;
  required string frame_id = 3 [default = "base_link"];
  optional apollo.localization.Pose pose_rel = 4;
}

message Point {
  required float x = 1;
  required float y = 2;
}

message RefineLoc {
  repeated float x_offset = 1;
  repeated float y_offset = 2;
  repeated float rotation = 3;
  optional uint64 timestamp = 4;
  optional apollo.localization.Pose pose_rel = 5;
}

message ImagePoint {
  required uint32 col = 1;
  required uint32 row = 2;
}

enum uLineType {
  CUT_NO = 0;
  CUT_PRE = 1;
  CUT_SUC = 2;
}

enum CutPointType {
  CUT_NOT = 1;       // 没有打断
  CUT_IN = 2;       // 打断保留
  CUT_OUT = 3;      // 打断不保留
};

message LaneInfo {
  repeated uint32 ids = 1;
  repeated Point pre_pts = 2;
  repeated Point suc_pts = 3;
  optional Point start_pt = 4;
  optional Point end_pt = 5;
  optional uLineType u_line_type = 6;
  optional int32 u_line_suc_id = 7;
  repeated CutPointType pre_type = 8;
  repeated CutPointType suc_type = 9;
}

message InPointInfo {
  optional uint32 intersection_id = 1;
  optional bool in_roi = 2;
  optional Point position = 3;
  optional Point direction = 4;
}

message LaneMapInfo {
  repeated LaneInfo lane_infos = 1;
  repeated InPointInfo in_pt_infos = 2;
}



message NaviInfo {
  repeated worldmodel.Lane lane_set = 1;
  optional worldmodel.RefineLoc refine_loc = 2;
}

message PerceptionInfo {
  optional worldmodel.FloatMatrix drivable_area = 1;
  repeated worldmodel.PidProb curb_probs = 2;
  repeated worldmodel.Curb curb_set = 3;
  repeated worldmodel.VectorizedLaneLine vectorized_laneline_set = 4;
  repeated worldmodel.VectorizedCenterLine vectorized_centerline_set = 5;
  repeated worldmodel.StopLine stopline_set = 6;
  repeated worldmodel.PidProb perception_crosswalk_area = 7;
  repeated worldmodel.PidProb perception_intersection_area = 8;
  repeated worldmodel.Lane perception_lane_set = 9;
  repeated worldmodel.MergeSplitPoint merge_split_point_set = 10;
  optional float reg_dis_to_intersection = 11;
  repeated worldmodel.Intersection perception_intersection_set = 12;
}


message MetaRoadStructure {
  optional NaviInfo navi_info = 1;
  optional PerceptionInfo perception_info = 2;
}

message DebugInfo {
  repeated float outputs_labels = 1;
  repeated float outputs_lanemarks_xy = 2;
  repeated float outputs_lanemarks_z = 3;
  repeated float outputs_dist_to_left = 4;
  repeated float outputs_dist_to_right = 5;
  repeated float outputs_validity = 6;
  repeated float outputs_visibility = 7;
  repeated float outputs_left_spanability = 8;
  repeated float outputs_right_spanability = 9;
  repeated float lanemarker_vetical_offset = 10;
  repeated float lanemarker_visible = 11;
}

message MetaRoadInfo {
  required Header header = 1;
  optional LaneMapInfo lanemap_infos = 2;
  optional robosense.rs_map.lanemap.LaneMap lanemap = 3;
  optional robosense.rs_map.lanemap.LaneMap localmap = 4;
  optional MetaRoadStructure meta_road_stucture = 5;
  optional DebugInfo debug_info = 6;
}

