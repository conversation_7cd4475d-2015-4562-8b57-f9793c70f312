syntax = "proto2";
package robosense.perception.worldmodel;

import "modules/rs_common_msgs/proto_world_model_msgs/Lane.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Intersection.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Crosswalk.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/LaneLight.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Curb.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/VectorizedLaneLine.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/StopLine.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_road.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap.proto";
import "modules/common_msgs/localization_msgs/pose.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Barrier.proto";

message Header {
  required uint32 seq = 1;
  required uint64 timestamp = 2;
  required string frame_id = 3 [default = "base_link"];
  optional float pose_status = 4 [default = 0.8];
  optional uint64 lidar_timestamp = 5;
  optional uint64 finish_timestamp = 6;
}

message FloatMatrix {
  repeated FloatArray rows = 1;
}

message FloatArray {
  repeated float values = 1;
}

message PidProb {
  optional uint32 id = 1;
  optional float prob = 2;
}

message RefineLoc {
  repeated float x_offset = 1;
  repeated float y_offset = 2;
  repeated float rotation = 3;
  optional uint64 timestamp = 4;
  optional apollo.localization.Pose pose_rel = 5;
  optional bool is_valid = 6;
}

message RoadStructure {
  required Header header = 1;
  optional FloatMatrix drivable_area = 2;
  repeated PidProb curb_probs = 3;
  repeated Intersection intersection_set = 4;
  repeated Lane lane_set = 5;
  repeated Crosswalk crosswalk_set = 6;
  repeated LaneLight lane_light_set = 7;
  repeated Curb curb_set = 8;
  repeated VectorizedLaneLine vectorized_laneline_set = 9;
  repeated VectorizedCenterLine vectorized_centerline_set = 10;
  repeated robosense.rs_map.lanemap.Road navi_road_set = 11;
  repeated StopLine stopline_set = 12;
  repeated PidProb perception_crosswalk_area = 13;
  repeated PidProb perception_intersection_area = 14;
  optional apollo.localization.Pose ref_pose = 15;
  repeated Point2D navi_points = 16;
  optional RefineLoc refine_loc = 17;
  repeated Lane perception_lane_set = 18;
  repeated MergeSplitPoint merge_split_point_set = 19;
  repeated ParkingBarrier barrier_set = 20;
  repeated robosense.rs_map.lanemap.MppSegment mpp_segment = 21;
  optional float reg_dis_to_intersection = 22;
  repeated Intersection perception_intersection_set = 23;
}
