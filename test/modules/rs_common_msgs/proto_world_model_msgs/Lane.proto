syntax = "proto2";
package robosense.perception.worldmodel;
import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_lane.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_stopline.proto";

enum LANECATEGORY {
  UNKNOWN_LANECATEGORY = 0;
  REALITY = 1;
  INTERSECTION_VIRTUAL = 2;
  M2N_VIRTUAL = 3;
  UNSTRUCTURED_VIRTUAL = 4;
  INTERSECTION_REALITY = 5;
}

enum LANETYPE {
  UNKNOWN_LANETYPE = 0;
  GENERAL = 1;
  EMERGENCY= 2;
  TIDAL =3;
  BUS = 4;
  UNCONV = 5;
  WAIT_LEFT = 6;    // 待左转车道
  WAIT_RIGHT = 7;   // 待右转
  WAIT_FORWARD = 8;  // 待直行
  ABNORMAL_LANE = 9; // 异常车道
  RIGHT_TURN_ONLY = 10; // 右转专用道
  VARIABLE_LANE = 11; // 可变车道
  U_TURN_LANE = 12; // 掉头车道
  TAXI = 13; // 出租车专用道
}

enum SPANABILITY {
  UNKNOWN_SPANABILITY = 0;
  SOLID = 1;
  DASHED = 2;
  VIRTUAL = 3;
}

enum LANEDIRECTION {
  UNKNOWN_LANEDIRECTION = 0;
  NOTURN = 1;
  LEFTTURN = 2;
  LEFTSTRAIGHT = 3;
  RIGHTTURN = 4;
  RIGHTSTRAIGHT = 5;
  LEFTRIGHTTURN = 6;
  LEFTRIGHTSTRAIGHT = 7;
  UTURN = 8;
  USTRAIGHT = 9;
  ULEFT = 10;
  USTRAIGHT_LEFT = 11;
  URIGHT = 12;
  USTRAIGHT_RIGHT = 13;
  ULEFTRIGHT = 14;
  ULEFTRIGHTSTRAIGHT = 15;
}

enum VISIBILITY {
  UNKNOWN_VISIBILITY = 0;
  VISIBLE = 1;
  UNVISIBLE = 2;
}

enum SPECIALAREA {
  UNKNOWN_AREA = 0;
  NORMAL = 1;
  MERGE = 2;
  SPLIT = 3;
  MERGESPLIT = 4;
}

enum STRUCTURAL {
  UNKNOWN_STRUCTURAL = 0;
  BOTH = 1;
  SINGLE = 2;
  NONE = 3;
}

message ROADMARKER {
  enum ROADMARKERCATEGORY {
    UNKNOWN_TYPE= 0;
    STRIGHT_FORWARD= 1;
    LEFT = 2;
    RIGHT = 3;
    LEFT_FORWARD = 4;
    RIGHT_FORWARD = 5;
    LEFT_RIGHT_FORWARD = 6;
    UTURN = 7;
    UTURN_RIGHT = 8;
    UTURN_FORWARD =9;
    UTURN_LEFT = 10;
    MERGE_LEFT = 11;
    MERGE_RIGHT = 12;
    LEFT_RIGHT =13;
    STOP = 14;
    OTHERS = 15;
    UTURN_LEFT_RIGHT = 16;
  }
}

message LightGroup {
  repeated string light_ids = 1;
}

message MergeSplitPoint {
  optional float x = 1;
  optional float y = 2;
  optional float odom_x = 3;
  optional float odom_y = 4;
  optional SPECIALAREA type = 5;
  repeated uint32 predecessor_lane_id_set = 6;
  repeated uint32 successor_lane_id_set = 7;
}

message Lane {
  optional uint32 lane_id = 1;
  repeated uint32 predecessor_lane_id_set = 2;
  repeated uint32 successor_lane_id_set = 3;
  repeated uint32 left_neighbor_lane_id_set = 4;
  repeated uint32 right_neighbor_lane_id_set = 5;
  repeated Point2D center_point_set = 6;
  repeated Point2D left_boundary_point_set = 7;
  repeated Point2D right_boundary_point_set = 8;
  repeated SPANABILITY left_spanability_set = 9;
  repeated SPANABILITY right_spanability_set = 10;
  optional LANETYPE lane_type = 11;
  optional LANEDIRECTION lane_direction = 12;
  repeated VISIBILITY visibility = 13;
  optional bool topo_filtered = 14 [default = false];
  optional bool is_intersection = 15 [default = false];
  repeated uint32 corresponding_intersection_id_set = 16;
  optional LANECATEGORY lane_category = 17;
  repeated Point2D stopline_position = 18;
  repeated LightGroup associated_lane_light_id = 19;
  repeated uint32 center_point_road_id = 20;
  optional bool visible_lane = 21 [default = false];
  optional bool pd_lane = 22 [default = false];
  optional bool navi_lane = 23 [default = false];
  repeated LightGroup associated_road_light_id = 24;
  repeated robosense.rs_map.lanemap.Lane.RSLaneType center_point_lane_type = 25;
  repeated robosense.rs_map.lanemap.Lane.RSRestrictedLaneType center_point_restricted_lane_type = 26;
  optional bool is_cutoff = 27 [default = false];
  repeated bool cutoff = 28;
  repeated uint32 connection_predecessor_lane_id_set = 29;
  repeated uint32 connection_successor_lane_id_set = 30;
  repeated Point2D roadmarker_position = 31;
  repeated ROADMARKER.ROADMARKERCATEGORY roadmarker_type = 32;
  repeated robosense.rs_map.lanemap.Stopline.Type stopline_type = 33;
  repeated robosense.rs_map.lanemap.Stopline.Visibility stopline_visibility = 34;
  repeated uint32 barrier_id_set = 35;
  optional robosense.rs_map.lanemap.Lane.LaneConvergeType converge_type = 36;      //  lane的 交汇属性类型
  optional robosense.rs_map.lanemap.Lane.LaneConnectionType connection_type = 37;      //  lane的 连接属性类型
  repeated SPECIALAREA special_area_type_set = 38;
}
