syntax = "proto2";
package robosense.perception.worldmodel;
import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";


enum BarrierStatus {
  OPEN = 0;
  CLOSE = 1;
}

enum BarrierType {
  BARRIER_UNKNOWN = 0;
  STRAIGHT_ARM_BARRIER = 1;  // 直杆
  FOLDING_ARM_BARRIER = 2; // 折杆
  FENCE_ARM_BARRIER = 3; // 栅栏
  BOLLARDS = 4; // 升降柱
}

message ParkingBarrier {
  optional uint32 barrier_id = 1;
  optional Point2D start = 2;
  optional Point2D end = 3;
  optional BarrierStatus status = 4;
  optional BarrierType type = 5;
  optional float pitch = 6;
}