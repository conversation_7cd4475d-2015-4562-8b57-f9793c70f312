syntax = "proto2";
package robosense.perception.worldmodel;

import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";

message LaneLight {
  optional uint64 timestamp = 1;
  optional robosense.rs_perception.trafficlight.MapMode map_mode = 2;
  optional string lane_light_id = 3;
  optional robosense.rs_perception.trafficlight.NaviType navi_type = 4;
  optional robosense.rs_perception.trafficlight.Color color = 5;
  optional float color_score = 6;

  optional robosense.rs_perception.trafficlight.GREEN_FLASH_TIME_STATE green_flash_time_state = 7;
  optional double green_flash_time = 8;
  optional robosense.rs_perception.trafficlight.YELLOW_TIME_STATE yellow_time_state = 9;
  optional double yellow_time = 10;

  optional int32 countdown = 11;
  optional string fused_light_id = 12;
  repeated uint32 extended_key = 13;
  repeated float extended_value = 14;

  optional uint32 group_id = 15;

  optional bool occlusion = 16;

  repeated uint32 associated_lane_id = 17;
  repeated bool belong_to_current_lane = 18;
  
  optional bool is_virtual = 19;

  repeated uint32 sorted_lane_id = 20;  
}
