syntax = "proto2";
package robosense.perception.worldmodel;
import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";
import "modules/rs_common_msgs/proto_world_model_msgs/Lane.proto";

message VectorizedLaneLine {
  optional uint32 laneline_id = 1;
  repeated Point2D vectorized_laneline = 2;
  repeated SPANABILITY laneline_type = 3;
}

message VectorizedCenterLine {
  optional uint32 centerline_id = 1;
  repeated Point2D vectorized_centerline = 2;
}