syntax = "proto2";
package robosense.perception.worldmodel;

import "modules/rs_common_msgs/proto_world_model_msgs/Geometry.proto";

enum TURNTYPE {
  UNKNOWN_TURNTYPE = 0;
  LEFT = 1;
  RIGHT = 2;
  STRAIGHT = 3;
  TURN_AROUND = 4;
}

enum INTERSECTIONTYPE {
  UNKNOWN_INTERSECTIONTYPE = 0;
  JUNCTION = 1;
  M2N = 2;
}

message TargetPoint {
  required Point2D position = 1;
  optional TURNTYPE turn_type = 2;
  required Point2D direction = 3;
}

message DirectedConnection {
  optional TURNTYPE turn_type = 1;
  optional uint32 src_lane_id = 2;
  optional uint32 dst_lane_id = 3;
}

message LaneGroup {
  repeated uint32 lane_ids = 1;
}

message Intersection {
  optional uint32 intersection_id = 1;
  optional INTERSECTIONTYPE intersection_type = 2;
  optional Polygon boundary = 3;
  repeated TargetPoint target_point_set = 4;
  repeated DirectedConnection lane_connections = 5;
  repeated LaneGroup lane_groups = 6;
  optional bool visible_intersection = 7 [default = false];
  optional bool pd_intersection = 8 [default = false];
  optional bool navi_intersection = 9 [default = false];
}
