syntax = "proto2";  

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto";

enum BlindType
{
    BLIND_UNKNOWN = 0;  // 默认类型
    BLIND_STATIC = 1;   // 被静目标遮挡
    BLIND_DYNAMIC = 2;  // 被动目标遮挡
};

message BlindSpotMapInfo
{
    optional uint32 length=1;     // 图层长 (对应x)
    optional uint32 width=2;      // 图层宽 (对应y)
    optional float min_x=3;       // 图层左下角x
    optional float min_y=4;       // 图层左下角y
    optional float max_x=5;       // 图层右上角x
    optional float max_y=6;       // 图层右上角y
    optional float resolution=7;  // 图层栅格分辨率
}

message BlindSpotMap
{
    optional robosense.std_msgs.Header header=1;
    optional robosense.geometry_msgs.Pose pose=2;
    optional BlindSpotMapInfo map_info=3;
    optional bytes id_map=4;      // 被遮挡目标id uint32_t
    optional bytes type_map=5;    // 盲区类型 uint8_t
    optional bytes height_map=6;  // 盲区高度 uint8_t
}
