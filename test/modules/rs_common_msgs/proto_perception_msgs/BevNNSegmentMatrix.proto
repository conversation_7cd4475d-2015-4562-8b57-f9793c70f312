syntax = "proto2";

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_perception_msgs/CSRMatrixU8.proto";

message BevNNSegmentMatrix
{
  required robosense.std_msgs.Header header=1;
  required uint32 sensor_id=2;

  required float xmin=3;
  required float ymin=4;
  required float vx=5;
  required float vy=6;

  required float pt_offset=7;
  required float pt_rot_cos=8;
  required float pt_rot_sin=9;

  required robosense.perception_msgs.CSRMatrixU8 csr_matrix=10;
}
