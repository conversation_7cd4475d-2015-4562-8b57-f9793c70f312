syntax = "proto2";

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";

enum SemanticType
{
    SEMANTIC_UNKNOWN = 0;
    SEMANTIC_GROUND = 1;
    SEMANTIC_GROUND_MIRROR = 2;
    SEMANTIC_HIGH_REF_ROAD_SIGN = 3;
    SEMANTIC_HIGH_REF_NOISE = 4;
    SEMANTIC_WATER_MIST = 5;
    SEMANTIC_BARRIER_GATE = 6;
}


message LidarSemantic
{
  required robosense.std_msgs.Header header=1;
  required bool is_valid=2;
  required uint32 sensor_id=3;
  required bytes semantic=4;
}
