
syntax = "proto2";  

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/common_msgs/perception_msgs/ObjectFusion.proto";
import "modules/rs_common_msgs/proto_perception_msgs/Curb.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto";

message ObjectFusionArray
{
    optional robosense.std_msgs.Header header=1;
    repeated ObjectFusion object_list=2;
    repeated robosense.perception_msgs.Curb curb_set=3;
    optional robosense.geometry_msgs.Pose pose=4;
}