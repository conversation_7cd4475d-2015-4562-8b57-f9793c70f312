
syntax = "proto2";  

package robosense.perception_msgs;

import "modules/common_msgs/perception_msgs/Vec2D.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto";
import "modules/rs_common_msgs/proto_std_msgs/Header.proto";

message OccupancyPillar
{
    optional uint32 row_index=1;
    optional uint32 col_index=2;
    optional float start_height=3;
    optional float end_height=4;
    optional uint32 label=5;
    optional uint32 id=6;
}

message OccupancyMapInfo
{
    optional uint32 width=1;
    optional uint32 height=2;
    optional float resolution=3;
    optional robosense.perception_msgs.Vec2D origin_coordinate=4;
    optional uint32 data_start_index=5;
    optional uint32 data_end_index=6;
}

message OccupancyMap
{
    optional robosense.std_msgs.Header header=1;
    optional robosense.geometry_msgs.Pose origin_pose=2;
    repeated robosense.perception_msgs.OccupancyMapInfo maps_info=3;
    repeated robosense.perception_msgs.OccupancyPillar maps_data=4;
}