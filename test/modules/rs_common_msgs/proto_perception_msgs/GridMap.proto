syntax = "proto2";  

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto";

message GridMapInfo
{
    optional uint32 length=1;     // 图层长 (对应x)
    optional uint32 width=2;      // 图层宽 (对应y)
    optional float min_x=3;       // 图层左下角x
    optional float min_y=4;       // 图层左下角y
    optional float max_x=5;       // 图层右上角x
    optional float max_y=6;       // 图层右上角y
    optional float resolution=7;  // 图层栅格分辨率
}

message GridMapData
{
    optional GridMapInfo map_info=1;  // 图层信息
    optional bytes map_data=2;        // 图层数据
}

message GridMap
{
    optional robosense.std_msgs.Header header=1;
    optional robosense.geometry_msgs.Pose pose=2;
    repeated GridMapData maps=3;
}
