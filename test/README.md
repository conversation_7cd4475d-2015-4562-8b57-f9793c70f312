# Proto Files Copy Summary

This directory contains copies of proto files and all their dependencies.

## Main Files
- `modules/common_msgs/control_msgs/control_cmd.proto` - The main control command proto file
- `modules/common_msgs/localization_msgs/localization.proto` - The main localization proto file
- `modules/common_msgs/planning_msgs/planning.proto` - The main planning proto file

## Direct Dependencies
- `modules/common_msgs/basic_msgs/drive_state.proto` - Drive state definitions
- `modules/common_msgs/basic_msgs/header.proto` - Common header definitions
- `modules/common_msgs/basic_msgs/pnc_point.proto` - Planning and control point definitions
- `modules/common_msgs/basic_msgs/vehicle_signal.proto` - Vehicle signal definitions
- `modules/common_msgs/chassis_msgs/chassis.proto` - Chassis message definitions
- `modules/common_msgs/control_msgs/input_debug.proto` - Input debug definitions
- `modules/common_msgs/control_msgs/pad_msg.proto` - Pad message definitions
- `modules/common_msgs/localization_msgs/localization_status.proto` - Localization status definitions
- `modules/common_msgs/localization_msgs/pose.proto` - Pose definitions
- `modules/common_msgs/map_msgs/map_id.proto` - Map ID definitions
- `modules/common_msgs/planning_msgs/decision.proto` - Planning decision definitions
- `modules/common_msgs/planning_msgs/planning_internal.proto` - Planning internal definitions
- `modules/common_msgs/planning_msgs/planning_status.proto` - Planning status definitions
- `modules/common_msgs/prediction_msgs/efficient_lane_change_feature.proto` - Efficient lane change feature definitions

## Indirect Dependencies
- `modules/common_msgs/basic_msgs/error_code.proto` - Error code definitions (required by header.proto)
- `modules/common_msgs/basic_msgs/geometry.proto` - Geometry definitions (required by chassis.proto)
- `modules/common_msgs/basic_msgs/vehicle_id.proto` - Vehicle ID definitions (required by chassis.proto)
- `modules/common_msgs/dreamview_msgs/chart.proto` - Chart definitions for dreamview
- `modules/common_msgs/map_msgs/*.proto` - Complete map message definitions (17 files)
- `modules/common_msgs/perception_msgs/*.proto` - Perception message definitions (9 files)
- `modules/common_msgs/planning_msgs/*.proto` - Additional planning message definitions (4 files)
- `modules/common_msgs/prediction_msgs/prediction_point.proto` - Prediction point definitions
- `modules/common_msgs/routing_msgs/routing.proto` - Routing message definitions

## Directory Structure
```
test/
└── modules/
    └── common_msgs/
        ├── basic_msgs/ (7 files)
        │   ├── drive_state.proto
        │   ├── error_code.proto
        │   ├── geometry.proto
        │   ├── header.proto
        │   ├── pnc_point.proto
        │   ├── vehicle_id.proto
        │   └── vehicle_signal.proto
        ├── chassis_msgs/ (1 file)
        │   └── chassis.proto
        ├── control_msgs/ (3 files)
        │   ├── control_cmd.proto
        │   ├── input_debug.proto
        │   └── pad_msg.proto
        ├── dreamview_msgs/ (1 file)
        │   └── chart.proto
        ├── localization_msgs/ (3 files)
        │   ├── localization.proto
        │   ├── localization_status.proto
        │   └── pose.proto
        ├── map_msgs/ (17 files)
        │   ├── map.proto
        │   ├── map_clear_area.proto
        │   ├── map_crosswalk.proto
        │   ├── map_geometry.proto
        │   ├── map_id.proto
        │   ├── map_junction.proto
        │   ├── map_lane.proto
        │   ├── map_overlap.proto
        │   ├── map_parking_space.proto
        │   ├── map_pnc_junction.proto
        │   ├── map_road.proto
        │   ├── map_rsu.proto
        │   ├── map_signal.proto
        │   ├── map_speed_bump.proto
        │   ├── map_stop_sign.proto
        │   └── map_yield_sign.proto
        ├── perception_msgs/ (9 files)
        │   ├── MotionType.proto
        │   ├── ObjectFusion.proto
        │   ├── ObjectType.proto
        │   ├── Size3D.proto
        │   ├── TrafficlightTypes.proto
        │   ├── Vec2D.proto
        │   ├── Vec3D.proto
        │   ├── perception_obstacle.proto
        │   └── traffic_light_detection.proto
        ├── planning_msgs/ (7 files)
        │   ├── decision.proto
        │   ├── navigation.proto
        │   ├── planning.proto
        │   ├── planning_internal.proto
        │   ├── planning_status.proto
        │   ├── scenario_type.proto
        │   └── sl_boundary.proto
        ├── prediction_msgs/ (2 files)
        │   ├── efficient_lane_change_feature.proto
        │   └── prediction_point.proto
        └── routing_msgs/ (1 file)
            └── routing.proto
```

**Total: 50 proto files**

All import paths in the proto files remain unchanged and should work correctly with this directory structure.
