syntax = "proto2";  
package robosense.diagnostic_msgs;
import "modules/rs_common_msgs/proto_diagnostic_msgs/KeyValue.proto"; 

message DiagnosticStatus
{
    enum LEVEL_TYPE 
    {
        OK = 0; 
        WARN = 1; 
        ERROR=2; 
        FATALE = 3; 
    }

    required LEVEL_TYPE level = 1;
    required string name = 2;
    required string message = 3;
    required string hardware_id = 4;
    repeated robosense.diagnostic_msgs.KeyValue values = 5;
}
