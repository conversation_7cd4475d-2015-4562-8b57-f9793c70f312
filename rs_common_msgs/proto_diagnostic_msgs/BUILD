## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "KeyValue_proto",
    srcs = ["KeyValue.proto"],
)

proto_library(
    name = "DiagnosticStatus_proto",
    srcs = ["DiagnosticStatus.proto"],
    deps = [
        ":KeyValue_proto",
    ],
)

proto_library(
    name = "DiagnosticArray_proto",
    srcs = ["DiagnosticArray.proto"],
    deps = [
        ":DiagnosticStatus_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

apollo_package()
