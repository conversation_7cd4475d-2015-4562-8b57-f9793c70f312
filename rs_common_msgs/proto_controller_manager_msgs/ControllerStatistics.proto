syntax = "proto2";  
package robosense.controller_manager_msgs;
import "modules/rs_common_msgs/proto_basic_msgs/time.proto";
import "modules/rs_common_msgs/proto_basic_msgs/duration.proto";

message ControllerStatistics 
{
    required string name = 1;
    required string type = 2;
    required robosense.basic_msgs.time timestamp = 3;
    required bool running = 4;
    required robosense.basic_msgs.duration max_time = 5;
    required robosense.basic_msgs.duration mean_tme = 6;
    required robosense.basic_msgs.duration variance_time = 7;
    required int32 num_control_loop_overruns = 8;
    required robosense.basic_msgs.time time_last_control_loop_overrun = 9;
}
