## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "ControllerState_proto",
    srcs = ["ControllerState.proto"],
    deps = [
        ":HardwareInterfaceResources_proto",
    ],
)

proto_library(
    name = "HardwareInterfaceResources_proto",
    srcs = ["HardwareInterfaceResources.proto"],
)

proto_library(
    name = "ControllersStatistics_proto",
    srcs = ["ControllersStatistics.proto"],
    deps = [
        ":ControllerStatistics_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "ControllerStatistics_proto",
    srcs = ["ControllerStatistics.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_basic_msgs:duration_proto",
        "//modules/rs_common_msgs/proto_basic_msgs:time_proto",
    ],
)

apollo_package()
