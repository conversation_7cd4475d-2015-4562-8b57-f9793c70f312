syntax = "proto2"; 

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_road.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_lane.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_intersection.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_m2n.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_unstructuredarea.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_ignorearea.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_stopline.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_trafficlight.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_staticobstacle.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_region.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_barrier.proto";

message Header 
{
    enum MapType {
      UNKNOWN = 0;
      ApolloHD = 1;
      Lanemap = 2;
    };
    required MapType maptype = 1;       // 当前地图类型，目前包括apollo hdmap以及自研的lanemap
    required string map_version = 2;         // map版本号
    required string tile_version = 3;        // tile版本号
    required uint64 tile_id = 4;             // tile id
    required PointLLA tile_point = 5;        // top left point of tile 
}

message Tile {
  required Header header = 1;   // 记录时间戳、版本号、地图类型等元信息
  repeated Road road = 2;       // road数组，在lanemap中相当于lane group，在hdmap中是road
  repeated Lane lane = 3;       // lane数组，lane是road中的具体车道
  repeated Intersection intersection = 4;  // intersection数组, 承载地图中的路口
  repeated Stopline stopline = 5;          // stopline数组, 承载地图中的停止线
  repeated TrafficLight trafficlight = 6;  // trafficlight数组, 承载地图中的交通灯
  repeated UnstructuredArea unstructured_area = 7; // unstructured_area数组, 承载lanemap地图中的非结构化区域，apollo hdmap中无此类元素
  repeated M2N m2n = 8;                            // m2n数组, 承载lanemap地图中的车道数量变化区域，apollo hdmap中无此类元素
  repeated IgnoreArea ignore_area = 9;   // ignore_area数组,承载lanemap地图中需要被忽略的区域，apollo hdmap中无此类元素
  repeated StaticObstacle staticobj = 10; // 道路中的静态障碍物
  repeated Region region = 11;           // 其余自定义区域，包括人行道、停车位等
  repeated ParkingBarrier barrier = 12;  // 横杆
}