syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

message PointUTM {   // utm系点的结构
  required double x = 1 [default = 0.0];  
  required double y = 2 [default = 0.0]; 
  required double z = 3 [default = 0.0];
  optional uint32 zone_id = 4;
}

message PointLLA {   // gps点结构
  // Longitude in degrees, ranging from -180 to 180.
  required double lon = 1 [default = 0.0];
  // Latitude in degrees, ranging from -90 to 90.
  required double lat = 2 [default = 0.0];
  // WGS-84 ellipsoid height in meters.
  optional double height = 3 [default = 0.0];
}

message PointUNI {   // 通用点的结构
  required double x = 1;  
  required double y = 2; 
  required double z = 3;  
  optional int32 id = 4;
}

message Point3D {
  required double x_odom = 1;  
  required double y_odom = 2; 
  required double z_odom = 3;  
}


message Polyline {  // 折线
  repeated robosense.rs_map.com_msgs.mapmsgs.PointUNI point = 1;
}

message Polygon {   // 多边形
  repeated robosense.rs_map.com_msgs.mapmsgs.PointUNI point = 1;
}

