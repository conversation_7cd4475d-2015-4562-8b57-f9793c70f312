syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";

message Link {
  optional uint64 in_lane_id = 1;
  optional uint64 out_lane_id = 2;

  enum LaneTurn {
    STRAIGHT = 1;           // 直行
    LEFT_TURN = 2;          // 左转
    LEFT_STRAIGHT = 3;      // 左转加直行
    RIGHT_TURN = 4;         // 右转
    RIGHT_STRAIGHT = 5;     // 右转加直行
    LEFT_RIGHT_TURN = 6;    // 可左转也可右转
    LEFT_RIGHT_STRAIGHT = 7;// 可直行可左右转
    U_TURN = 8;             // 掉头
    U_STRAIGHT = 9;         // 可直行可掉头
    U_LEFT = 10;            // 可掉头可左转
    U_STRAIGHT_LEFT = 11;   // 可直行可掉头可左转
    U_RIGHT = 12;           // 可掉头右转
    U_STRAIGHT_RIGHT = 13;  // 可直行可掉头可右转
    U_LEFT_RIGHT = 14;      // 可掉头可左转可右转
    U_LEFT_RIGHT_STRAIGHT = 15; // 可掉头可左转可右转可直行
  };
  optional LaneTurn turn_type = 3;
}

message Intersection {
  required uint64 id = 1;              // 当前路口的全局唯一id
  required Polygon polygon = 2;        // 当前路口的多边形

  enum Type {
    UNKNOWN = 0;
    CROSS_ROAD = 1;
  };
  required Type type = 3;               // 路口类型

  repeated uint64 predecessor_road_id = 4;      // 与路口连接的前继road的id
  repeated uint64 successor_road_id = 5;        // 与路口连接的后继road的id

  repeated Link link = 6;
}
