syntax = "proto2";

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";
import "modules/rs_common_msgs/proto_map_msgs/map_intersection.proto";

message LaneBoundary {   // 边界对象
  required Polyline boundary = 1;  // lane 边界线

  enum Boundarytype {
    VIRTUALLINE = 0;
    SOLIDLINE = 1;
    DASHEDLINE = 2;
    LEFT_DOUBLESOLIDLINE = 3;
    RIGHT_DOUBLESOLIDLINE = 4;
    LEFT_DOUBLEDASHEDLINE = 5;
    RIGHT_DOUBLEDASHEDLINE = 6;
    LEFT_DOTTEDSOLIDLINE = 7;
    RIGHT_DOTTEDSOLIDLINE = 8;
    LEFT_SOLIDDOTTEDLINE = 9;
    RIGHT_SOLIDDOTTEDLINE = 10;
    PHYSICALLINE = 11;
    OTHERS = 100;
  };
  required Boundarytype boundary_type = 2; // 边界类型

  enum Boundarycolor {
    WHITE = 0;
    YELLOW = 1;
    NONE = 2;
    OTHER_COLORS = 100;
  };
  required Boundarycolor boundary_color = 3; // 边界颜色
}

message Lane {
  required uint64 id = 1;                    // lane的全局唯一id
  required Polyline skeleton = 2;            // lane的骨架线
  optional LaneBoundary left_boundary = 3;   // lane的左边界（lanemap地图不发出边界信息，hdmap发出）
  optional LaneBoundary right_boundary = 4;  // lane的右边界（lanemap地图不发出边界信息，hdmap发出）

  enum LaneType {
    UNKNOWN = -1;   // 未知
    NORMAL = 0;     // 通用车道
    EMERGENCY = 1;  // 应急车道
    TIDE = 2;       // 潮汐车道
    BUS = 3;        // 公交车道
    UNCONVENTION = 4; //非机动车道
    WAIT_LEFT = 5;    // 待左转车道
    WAIT_RIGHT = 6;   // 待右转
    WAIT_FORWARD =7;  //待直行
    ABNORMAL_LANE = 8; // 异常车道
    RIGHT_TURN_ONLY = 9; // 右转专用道
    VARIABLE_LANE = 10; // 可变车道
    U_TURN_LANE = 11; // 掉头车道
    TAXI = 12; // 出租车专用道
  };
  required LaneType type = 5;      //  lane的类型，包括上面枚举

  enum LaneTurn {
    STRAIGHT = 1;           // 直行
    LEFT_TURN = 2;          // 左转
    LEFT_STRAIGHT = 3;      // 左转加直行
    RIGHT_TURN = 4;         // 右转
    RIGHT_STRAIGHT = 5;     // 右转加直行
    LEFT_RIGHT_TURN = 6;    // 可左转也可右转
    LEFT_RIGHT_STRAIGHT = 7;// 可直行可左右转
    U_TURN = 8;             // 掉头
    U_STRAIGHT = 9;         // 可直行可掉头
    U_LEFT = 10;            // 可掉头可左转
    U_STRAIGHT_LEFT = 11;   // 可直行可掉头可左转
    U_RIGHT = 12;           //  可掉头右转
    U_STRAIGHT_RIGHT = 13;  // 可直行可掉头可右转
    U_LEFT_RIGHT = 14;      // 可掉头可左转可右转
    U_LEFT_RIGHT_STRAIGHT = 15; // 可掉头可左转可右转可直行
  };

  optional LaneTurn turn = 6;  // lane的车道方向

  repeated uint64 predecessor_id = 7; // 物理前继的车道id数组
  repeated uint64 successor_id = 8;   // 物理后继的车道id数组

  optional uint64 ref_road_id = 9;            // 当前lane属于哪些road，记录road的uid
  optional uint64 traffic_light_id = 10;   // 当前lane由哪个红绿灯id控制


  repeated Link lane_link = 11; // 虚拟拓扑 连接

  enum LanePhysicalType {
    REAL = 0;
    VIRTUAL = 1;
    WIDE_MERGE = 2;
  };

  enum LaneConvergeType {
    CONVERGE_NONE = 0; // 无类型
    CONVERGE_INWARDS = 1; // 汇入车道
    CONVERGE_OUTWARDS = 2; // 汇出车道
    CONVERGE_INOUTWARDS = 3; // 汇入+汇出车道 (X 型交叉车道)
  };

  enum LaneConnectionType {
    CONNECTION_NORMAL = 0; // 普通
    CONNECTION_SPLITING = 1; // 分流车道
    CONNECTION_MERGING = 2; // 合流车道
  };

  optional LanePhysicalType physical_type = 12;      //  lane的物理属性类型

  repeated uint64 virtual_predecessor_id = 13; // 虚拟前继的车道id数组
  repeated uint64 virtual_successor_id = 14;   // 虚拟后继的车道id数组

  optional LaneConvergeType converge_type = 15;      //  lane的 交汇属性类型
  optional LaneConnectionType connection_type = 16;      //  lane的 连接属性类型

  repeated uint64 wide_lane_set = 17;             // 宽车道
}
