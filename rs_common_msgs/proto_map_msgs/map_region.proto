syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";

message Region {
  optional uint64 id = 1;            // 当前区域的全局唯一id
  optional Polygon polygon = 2;      // 区域的多边形轮廓
  repeated uint64 ref_road_id = 3;   // 当前区域属于哪些road，记录road的id
  
  enum RegionType {
    OTHER = 0;        // 其他
    SIDEWALK = 2;     // 人行道
    PARKING = 3;      // 停车位
    CROSSWALK = 4;
  };
  optional RegionType type = 4;        // 区域的类型
}
