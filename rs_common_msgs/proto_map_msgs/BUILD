## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "map_lane_proto",
    srcs = ["map_lane.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_intersection_proto",
    ],
)

proto_library(
    name = "map_proto",
    srcs = ["map.proto"],
    deps = [
        ":map_barrier_proto",
        ":map_geometry_proto",
        ":map_ignorearea_proto",
        ":map_intersection_proto",
        ":map_lane_proto",
        ":map_m2n_proto",
        ":map_region_proto",
        ":map_road_proto",
        ":map_staticobstacle_proto",
        ":map_stopline_proto",
        ":map_trafficlight_proto",
        ":map_unstructuredarea_proto",
    ],
)

proto_library(
    name = "map_intersection_proto",
    srcs = ["map_intersection.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "PointCloud2Update_proto",
    srcs = ["PointCloud2Update.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_sensor_msgs:PointCloud2_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "map_trafficlight_proto",
    srcs = ["map_trafficlight.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_m2n_proto",
    srcs = ["map_m2n.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_road_proto",
    srcs = ["map_road.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_barrier_proto",
    srcs = ["map_barrier.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_unstructuredarea_proto",
    srcs = ["map_unstructuredarea.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "ProjectedMapInfo_proto",
    srcs = ["ProjectedMapInfo.proto"],
)

proto_library(
    name = "ProjectedMap_proto",
    srcs = ["ProjectedMap.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_nav_msgs:OccupancyGrid_proto",
    ],
)

proto_library(
    name = "OccupancyGridUpdate_proto",
    srcs = ["OccupancyGridUpdate.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "map_stopline_proto",
    srcs = ["map_stopline.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_ignorearea_proto",
    srcs = ["map_ignorearea.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_geometry_proto",
    srcs = ["map_geometry.proto"],
)

proto_library(
    name = "map_region_proto",
    srcs = ["map_region.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_staticobstacle_proto",
    srcs = ["map_staticobstacle.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

apollo_package()
