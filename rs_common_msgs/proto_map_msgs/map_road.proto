syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";

enum RoadType {
    NORMAL = 0;              // 结构化Lanegroup
    UNSTRUCTURED = 1;        // 非结构化Lanegroup
    WITHIN_INTERSECTION = 2; // 路口内道路
    MAIN_LOAD = 3;           // 主车道
    SIDE_LOAD = 4;           // 辅车道
    MAINROAD_CONNECTION = 5; // 主辅连接车道
}

message RoadBoundary {
    required uint64 id = 1;
    required Polyline points = 2;
}

message Road {
  required uint64 id = 1;              // road的全局唯一id
  required Polygon polygon = 2;        // road的多边形轮廓
  required Polyline polyline = 3;      // 表征road的参考线
  
  repeated uint64 predecessor_id = 4; // road的前继id数组，这里的前后继元素包括路口和road
  repeated uint64 successor_id = 5;   // road的后继id数组，这里的前后继元素包括路口和road
  
  repeated uint64 lane_set = 6;               // 当前road包含的lane的id
  repeated uint64 stopline_set = 7;           // 当前road包含的stopline的id
  repeated uint64 unstructured_area_set = 8;  // 当前road包含的unstructured_area的id
  repeated uint64 m2n_set = 9;                // 当前road包含的m2n的id
  repeated uint64 staticobj_set = 10;         // 当前road包含的staticobj的id
  repeated uint64 region_set = 11;            // 当前road包含的其他自定义区域的id
  repeated uint64 traffic_light_id = 12;      // 当前road包含的红绿灯的id
  
  optional RoadBoundary left_boundary = 13;    // road左边界
  optional RoadBoundary right_boundary = 14;   // road右边界
  required RoadType road_type = 15;            // road类型
}