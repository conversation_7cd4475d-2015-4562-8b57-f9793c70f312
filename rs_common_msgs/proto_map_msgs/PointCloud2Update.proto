syntax = "proto2";  
package robosense.map_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 
import "modules/rs_common_msgs/proto_sensor_msgs/PointCloud2.proto"; 



message PointCloud2Update
{
    enum UPDATE_TYPE 
    {
        ADD = 0; 
        DELETE = 1; 
    }

    required robosense.std_msgs.Header header = 1;
    required UPDATE_TYPE type = 2;
    required robosense.sensor_msgs.PointCloud2 points = 3;
}
