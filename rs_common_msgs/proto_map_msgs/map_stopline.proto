syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";

message Stopline {
  required uint32 id = 1;               // 停止线全局唯一id
  required Polyline line = 2;           // 停止线几何线段

  enum Type {
    NORMAL = 0;
    WAITTING = 1;
  };
  required Type type = 3;                // 停止线类型

  enum Visibility {
    ENTITY = 0;
    VIRTUAL = 1;
  };
  optional Visibility visibility = 4;    // 停止线可见性
  optional uint64 ref_lane_id = 5;       // 当前停止线和哪个lane id绑定
  optional uint64 ref_road_id = 6;
}
