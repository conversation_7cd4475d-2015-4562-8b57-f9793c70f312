syntax = "proto2";  

package robosense.rs_map.com_msgs.mapmsgs;

import "modules/rs_common_msgs/proto_map_msgs/map_geometry.proto";

message IgnoreArea {
  required uint64 id = 1;         // 当前无效区域的全局唯一id
  required Polygon polygon = 2;   // 无效区域的多边形轮廓
  repeated uint64 ref_road_id = 3;   // 当前无效区域属于哪些road，记录road的id
  
  enum IgnoreType {
    NORMAL = 0;     // 常规无效区域
    SLAMFAIL = 1;   // 建图失败、重影
    OTHER = 2;      // 其他无效区域
  };
  required IgnoreType type = 4;        // 无效区域的类型
}
