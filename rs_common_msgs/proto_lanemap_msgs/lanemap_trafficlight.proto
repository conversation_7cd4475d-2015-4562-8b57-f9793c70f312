syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";

message BoxSize {
  optional float height = 1;
  optional float width = 2;
  optional float length = 3;
}

message TrafficLight {
  optional uint32 id = 1;
  optional uint32 group_id = 2;

  enum Type {
    UNKNOWN = 0;
    FORWARD = 1;
    LEFT = 2;
    RIGHT = 4;
    UTURN = 8;
    FORWARD_LEFT = 3;
    FORWARD_RIGHT = 5;
    FORWARD_UTURN = 9;
    LEFT_RIGHT = 6;
    LEFT_UTURN = 10;
    RIGHT_UTURN = 12;
    FORWARD_LEFT_RIGHT = 7;
    FORWARD_LEFT_UTURN = 11;
    FORWARD_RIGHT_UTURN = 13;
    LEFT_RIGHT_UTURN = 14;
    FORWARD_LEFT_RIGHT_UTURN = 15;
  }
  optional Type navi_type = 3;
  required Point3D center_odom = 4;
  optional BoxSize box_size = 5;
  optional float heading = 6;
  repeated Point3D polygon = 7;
  repeated uint32 lane_id = 8;
  repeated uint32 road_id = 9;
  optional bool topo_filtered = 10;
}
