syntax = "proto2";  

package robosense.rs_map.lanemap;

message PointUTM {
  optional float x = 1 [default = nan];  
  optional float y = 2 [default = nan]; 
  optional float z = 3 [default = 0.0];
}

message PointGPS {
  // Longitude in degrees, ranging from -180 to 180.
  optional float lon = 1 [default = nan];
  // Latitude in degrees, ranging from -90 to 90.
  optional float lat = 2 [default = nan];
  // WGS-84 ellipsoid height in meters.
  optional float height = 3 [default = 0.0];
}

message PointUNI {
  optional float x = 1;  
  optional float y = 2; 
  optional float z = 3;  
  optional uint32 id = 4;
}

message Point3D {
  required double x_odom = 1;  
  required double y_odom = 2; 
  required double z_odom = 3;  
}


message Linesegment {
  repeated robosense.rs_map.lanemap.PointUNI point = 1;
}

message Polygon {
  repeated robosense.rs_map.lanemap.PointUNI point = 1;
}
