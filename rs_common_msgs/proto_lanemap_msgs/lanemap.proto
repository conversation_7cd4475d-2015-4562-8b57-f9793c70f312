syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_lane.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_intersection.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_m2n.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_unstructuredarea.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_ignorearea.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_crosswalk.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_road.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_stopline.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_trafficlight.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_barrier.proto";

enum LanemapMode
{
  LANEMAP = 1;
  MAPFREE = 2; // 新版mapfree，由mapnode设定
}

enum LanemapType
{
  RS = 1;
  BD = 2;
}


message Header
{
    optional uint32 seq = 1;
    optional string nsec_timestamp = 2;
    optional string frame_id = 3;
    optional int32 is_valid = 4 [default = 0];
    optional float overlap = 5;
    optional string capsulename = 6;

    optional double pose_utmx = 7;
    optional double pose_utmy = 8;
    optional double pose_utmz = 9;
    optional double pose_qw = 10;
    optional double pose_qx = 11;
    optional double pose_qy = 12;
    optional double pose_qz = 13;
    optional string zone_id = 14;

    optional float loc_status = 15;
    optional LanemapType lanemap_type = 16;
    optional LanemapMode lanemap_mode = 17;
}

message MppPath {
  repeated uint32 link = 1;
}

message MppSegment {
  repeated MppPath path = 1;
}

message LaneMap {
  optional Header header = 1;
  repeated Lane lane = 2;
  repeated Intersection intersection = 3;
  repeated Stopline stopline = 4;
  repeated UnstructuredArea unstructured_area = 5;
  repeated M2N m2n = 6;
  repeated IgnoreArea ignore_area = 7;
  repeated Road road = 8;
  repeated Crosswalk crosswalk = 9;
  repeated TrafficLight traffic_light = 10;
  repeated PointUNI navi_points=11; // 弃用
  repeated Linesegment curb =  12;

  optional uint32 mpp_status = 13; // mpp状态位，目前仅判断0/1
  repeated MppSegment mpp_segment = 14;
  repeated ParkingBarrier barrier = 15;
}
