syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";

message LaneMarking
{
  required uint32 marking_id = 1;
  required Linesegment marking_skeleton = 2;
  enum DividerMarkingType{
    DMT_MARKING_NONE = 0; // 无属性
    DMT_MARKING_LONG_DASHED_LINE = 1; // 长虚线
    DMT_MARKING_DOUBLE_SOLID_LINE = 2; // 双实线
    DMT_MARKING_SINGLE_SOLID_LINE = 3; // 单实线
    DMT_MARKING_RIGHT_SOLID_LINE_LEFT_DASHED_LINE = 4; // 右实左虚线
    DMT_MARKING_LEFT_SOLID_LINE_RIGHT_DASHED_LINE = 5;  // 左实右虚线
    DMT_MARKING_DOUBLE_DASHED_LINE = 9; // 双虚线
    DMT_MARKING_VIRTUAL_FIT = 22; // 虚拟边线（编译）
    DMT_MARKING_VIRTUAL = 23; // 虚拟边线（生产）
    DMT_MARKING_UNKNOWN = 254; // 目前未使用，用于之后扩展
  };
  required DividerMarkingType marking_type = 3;

  enum RoadBoundaryType {
    ROAD_BOUNDARY_TYPE_NONE = 0; // 无
    ROAD_BOUNDARY_TYPE_CURB = 1; // 路沿
    ROAD_BOUNDARY_TYPE_BARRIER = 2; // 护栏
    ROAD_BOUNDARY_TYPE_NATURAL = 3; // 自然边界
    ROAD_BOUNDARY_TYPE_VIRTUAL = 4; // 虚拟边界
    ROAD_BOUNDARY_TYPE_DITCH = 5; // 沟
  };

  optional RoadBoundaryType road_boundary_type = 4;
  optional bool is_road_boundary = 5;
}

message LaneMarkings
{
  repeated LaneMarking lane_marking = 2;
}

message Lane {
  optional uint32 id = 1;

  optional Linesegment skeleton = 2;

  enum LaneType {
    NORMAL = 1;       // 通用车道
    EMERGENCY = 2;    // 应急车道
    TIDE = 3;         // 潮汐车道
    BUS = 4;          // 公交车道
    UNKNOWN = 5;      // 未知
    UNCONVENTION = 6; // 非机动车道
    WAIT_LEFT = 7;    // 待左转车道
    WAIT_RIGHT = 8;   // 待右转
    WAIT_FORWARD = 9;  // 待直行
    ABNORMAL_LANE = 10; // 异常车道
    RIGHT_TURN_ONLY = 11; // 右转专用道
    VARIABLE_LANE = 12; // 可变车道
    U_TURN_LANE = 13; // 掉头车道
    TAXI   = 14; // 出租车专用道
  };
  optional LaneType type = 3;  

  enum RSLaneType {
    LAT_NORMAL                  = 0;  // 普通
    LAT_ENTRY                   = 1;  // 入口
    LAT_EXIT                    = 2;  // 出口
    LAT_EMERGENCY               = 3;  // 应急车道
    LAT_ON_RAMP                 = 4;  // 进入匝道
    LAT_OFF_RAMP                = 5;  // 退出匝道
    LAT_CONNECT_RAMP            = 6;  // 连接匝道
    LAT_ACCELERATE              = 7;  // 加速车道
    LAT_DECELERATE              = 8;  // 减速车道
    LAT_EMERGENCY_PARKING_STRIP = 9;  // 紧急停车带
    LAT_CLIMBING                = 10; // 爬坡车道
    LAT_ESCAPE                  = 11; // 避险车道
    LAT_DEDICATED_CUSTOMS       = 12; // 海关专用车道
    LAT_VIEWING_PLATFROM        = 13; // 观景台车道
    LAT_PARALLEL_LANE           = 14; // 平行车道
    LAT_DIVERSION               = 17; // 导流区车道
    LAT_PARKING                 = 19; // 停车车道
    LAT_LEFT_TURN_AREA          = 20; // 左转等待车道
    LAT_VARIABLE_LANE           = 21; // 可变车道
    LAT_NON_MOTOR_LANE          = 22; // 非机动车道
    LAT_BUS_STOP                = 23; // 公交停靠站
    LAT_NO_ENTRY_LANE           = 24; // 不可行驶车道
    LAT_U_TURN_LANE             = 25; // 掉头车道
    LAT_RIGHT_TURN_LANE         = 26; // 右转专用车道
    LAT_ETC_LANE                = 28; // ETC车道
    LAT_MANUAL_TOLL_LANE        = 29; // 人工收费车道
    LAT_PLAZA_LANE              = 30; // 收费站外广场车道
    LAT_HARBOR_STOP             = 31; // 港湾停靠站
    LAT_RIGHT_TURN_AREA         = 32; // 右转等待车道
    LAT_U_TURN_AREA             = 33; // 掉头等待车道
    LAT_NO_TURN_AREA            = 34; // 直行等待车道
    LAT_OTHER                   = 254; // 其他车道
  };

  enum RSRestrictedLaneType {
    RESTRICTED_LANE_TYPE_NONE   = 0; // 无
    RESTRICTED_LANE_TYPE_TIDAL  = 1; // 潮汐车道
    RESTRICTED_LANE_TYPE_BRT    = 2; // 公交专用道
    RESTRICTED_LANE_TYPE_HOV    = 3; // HOV车道
    RESTRICTED_LANE_TYPE_TAXI   = 4; // 出租车专用道
    RESTRICTED_LANE_TYPE_OTHER  = 5; // 其他车道
  };

  enum LaneTurn {
    NO_TURN = 1;
    LEFT_TURN = 2;
    LEFT_STRAIGHT = 3;
    RIGHT_TURN = 4;
    RIGHT_STRAIGHT = 5;
    LEFT_RIGHT_TURN = 6;
    LEFT_RIGHT_STRAIGHT = 7;
    U_TURN = 8;
    U_STRAIGHT = 9;
    U_LEFT = 10;
    U_STRAIGHT_LEFT = 11;
    U_RIGHT = 12;
    U_STRAIGHT_RIGHT = 13;
    U_LEFT_RIGHT = 14;
    U_LEFT_RIGHT_STRAIGHT = 15;
  };

  optional LaneTurn turn = 4;

  repeated uint32 predecessor_id = 5;
  repeated uint32 successor_id = 6;

  optional bool topo_filtered = 7 [default = true];

  repeated uint32 road_id = 8;

  repeated uint32 left_id = 9;
  repeated uint32 right_id = 10;

  enum LaneColor{
    RED = 1;
    GREEN = 2;
    BLUE= 3;
    WHITE = 4;
    YELLOW = 5;
    BLACK = 6;
    PURPLE = 7;
    ORANGE = 8;
    PINK = 9;
    OTHER = 10;
  };
  // 黑色（Black）：cv::Scalar(0, 0, 0)
  // 白色（White）：cv::Scalar(255, 255, 255)
  // 红色（Red）：cv::Scalar(0, 0, 255)
  // 绿色（Green）：cv::Scalar(0, 255, 0)
  // 蓝色（Blue）：cv::Scalar(255, 0, 0)
  // 黄色（Yellow）：cv::Scalar(0, 255, 255)
  // 紫色（Purple）：cv::Scalar(128, 0, 128)
  // 青色（Cyan）：cv::Scalar(255, 255, 0)
  // 橙色（Orange）：cv::Scalar(0, 165, 255)
  // 粉红色（Pink）：cv::Scalar(255, 192, 203)
  optional LaneColor color = 11;


  optional LaneMarkings left_marking = 12;
  optional LaneMarkings right_marking = 13;

  optional bool cutoff = 14 [default = false];
  optional uint32 cutoff_start_index = 15;
  optional uint32 cutoff_end_index = 16;
  optional bool cutoff_inverse = 17;


  repeated RSLaneType short_rs_lanetype = 18; // origin laneid type, correspond to road id
  repeated RSRestrictedLaneType short_rs_restricted_lanetype = 19;

  message Link
  {
    optional uint64 in_lane_id = 1;
    optional uint64 out_lane_id = 2;
    optional LaneTurn turn_type = 3;
  };

  repeated Link lane_link = 20;

  enum LanePhysicalType {
    REAL = 0;
    VIRTUAL = 1;
    WIDE_MERGE = 2;
  };

  enum LaneConvergeType {
    CONVERGE_NONE = 0; // 无类型
    CONVERGE_INWARDS = 1; // 汇入车道
    CONVERGE_OUTWARDS = 2; // 汇出车道
    CONVERGE_INOUTWARDS = 3; // 汇入+汇出车道 (X 型交叉车道)
  };

  enum LaneConnectionType {
    CONNECTION_NORMAL = 0; // 普通
    CONNECTION_SPLITING = 1; // 分流车道
    CONNECTION_MERGING = 2; // 合流车道
  };

  optional LanePhysicalType lane_physical_type = 21;

  repeated uint32 virtual_predecessor_id = 22;
  repeated uint32 virtual_successor_id = 23;

  optional LaneConvergeType converge_type = 24;      //  lane的 交汇属性类型
  optional LaneConnectionType connection_type = 25;      //  lane的 连接属性类型


  repeated uint64 wide_lane_set = 26;             // 宽车道

}
