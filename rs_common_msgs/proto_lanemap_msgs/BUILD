## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "lanemap_unstructuredarea_proto",
    srcs = ["lanemap_unstructuredarea.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_crosswalk_proto",
    srcs = ["lanemap_crosswalk.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_barrier_proto",
    srcs = ["lanemap_barrier.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_stopline_proto",
    srcs = ["lanemap_stopline.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_geometry_proto",
    srcs = ["lanemap_geometry.proto"],
)

proto_library(
    name = "lanemap_m2n_proto",
    srcs = ["lanemap_m2n.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_intersection_proto",
    srcs = ["lanemap_intersection.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_proto",
    srcs = ["lanemap.proto"],
    deps = [
        ":lanemap_barrier_proto",
        ":lanemap_crosswalk_proto",
        ":lanemap_geometry_proto",
        ":lanemap_ignorearea_proto",
        ":lanemap_intersection_proto",
        ":lanemap_lane_proto",
        ":lanemap_m2n_proto",
        ":lanemap_road_proto",
        ":lanemap_stopline_proto",
        ":lanemap_trafficlight_proto",
        ":lanemap_unstructuredarea_proto",
    ],
)

proto_library(
    name = "lanemap_ignorearea_proto",
    srcs = ["lanemap_ignorearea.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_road_proto",
    srcs = ["lanemap_road.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_trafficlight_proto",
    srcs = ["lanemap_trafficlight.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

proto_library(
    name = "lanemap_lane_proto",
    srcs = ["lanemap_lane.proto"],
    deps = [
        ":lanemap_geometry_proto",
    ],
)

apollo_package()
