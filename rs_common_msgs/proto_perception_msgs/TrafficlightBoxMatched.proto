syntax = "proto2";

package robosense.rs_perception.trafficlight;

import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBox2D.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBox3D.proto";


message TrafficlightBoxMatched {
  required TrafficlightBox2D bbox2d = 1;
  optional double confidence = 2;
  optional TrafficlightBox3D relative_pos = 3;
  optional TrafficlightBox3D absolute_pos = 4;
  optional Color color = 5;
  optional Source source = 6;
  // track ID是视觉后处理后的结果
  optional string track_id = 7;
  // Traffic light string-ID in the map data.
  optional string map_id = 8;
  optional NaviType navi_type = 9;
  optional bool flash = 10;
  // Duration of the traffic light since detected.
  optional double tracking_time = 11;
  repeated CornerPointInEgo tl_corners = 12;
}