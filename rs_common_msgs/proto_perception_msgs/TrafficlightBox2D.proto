syntax = "proto2";

package robosense.rs_perception.trafficlight;
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";


message TrafficlightBox2D {
  required CameraID camera_id = 1;
  required int32 x = 2;
  required int32 y = 3;
  optional int32 width = 4;
  optional int32 height = 5;
  optional Color color = 6;
  optional double color_score = 7;
  optional Shape shape = 8;
  optional double shape_score = 9;
  optional int32 heading = 10;
  optional int32 bumb_num = 11;
  optional bool mainlight = 12;
  optional double mainlight_score = 13;
  optional bool occlusion = 14;
  optional double occlusion_score = 15;
  optional int32 countdown = 16;
  optional double confidence = 17;
  optional string map_id = 18;
  optional bool is_in_frame = 19;

  optional double match_x_pix = 20; // 框与哪个框中心相连，用于debug match
  optional double match_y_pix = 21;

  // Flashing color. When color is red, yellow and green, it is consistent with color. When color is black, it exists independently.
  optional Color flash_color = 22;
  optional double flash_score = 23;
  optional double countdown_score = 24;

  // add each prob(0-9) of single number of countdown
  repeated double countdown_1 = 25;  // 个位
  repeated double countdown_10 = 26;  // 十位
  repeated double countdown_100 = 27;  // 百位

  // bulb_center
  optional int32 bulb_center_x = 28;
  optional int32 bulb_center_y = 29;

  // add det scale, example:  narrow full image: 0.4, narrow crop: 1.0, wide full image: 0.4, wide crop: 0.72
  optional double scale = 30;

  optional bool control_veh = 31;
  optional float control_veh_score = 32;

  // add crop info after merge crop and full image
  optional bool is_crop = 33;
  optional bool merge_full = 34;
  optional Color full_flash_color = 35;
  optional double full_flash_score = 36;
}
