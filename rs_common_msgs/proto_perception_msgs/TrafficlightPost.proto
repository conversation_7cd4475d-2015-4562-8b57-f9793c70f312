syntax = "proto2";

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_basic_msgs/time.proto";

import "modules/common_msgs/perception_msgs/Vec3D.proto";
import "modules/common_msgs/perception_msgs/BBox2D.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";
import "modules/common_msgs/planning_msgs/planning_status.proto";

message DetectLight {
    optional float sys_timestamp = 1;
    optional float det_timestamp = 2;

    optional robosense.rs_perception.trafficlight.CameraID sensor_id = 3;
    optional robosense.perception_msgs.BBox2D box = 4;

    optional robosense.rs_perception.trafficlight.Shape shape = 5;
    optional float shape_score = 6;
    optional robosense.rs_perception.trafficlight.Color color = 7;
    optional float color_score = 8;

    optional bool mainlight = 9;
    optional float mainlight_score = 10;

    optional bool occlusion = 11;
    optional float occlusion_score = 12;

    optional int32 countdown = 13;

    optional int32 match_x_pix = 14;
    optional int32 match_y_pix = 15;

    optional float heading = 16;
    optional float heading_obs = 17;

    optional float real_width_pix = 18;
    optional float real_height_pix = 19;
}

message FrameFusedLight {
    required robosense.basic_msgs.time timestamp = 1;
    required robosense.rs_perception.trafficlight.MapMode map_mode = 2;
    required string fused_light_id = 3;

    required robosense.rs_perception.trafficlight.Shape shape = 4;
    required robosense.rs_perception.trafficlight.Color color = 5;
    required float color_score = 6;

    repeated robosense.perception_msgs.Vec3D corner_points = 7;

    required bool occlusion = 8;
    required int32 countdown = 9;

    repeated DetectLight detect_lights = 10;

    required robosense.rs_perception.trafficlight.GREEN_FLASH_TIME_STATE green_flash_time_state = 11;
    required double green_flash_time = 12;
    required robosense.rs_perception.trafficlight.YELLOW_TIME_STATE yellow_time_state = 13;
    required double yellow_time = 14;

    repeated int32 extended_key = 15;
    repeated float extended_value = 16;

    repeated robosense.perception_msgs.Vec3D corner_points_odom = 17;

    optional float heading = 18;
    optional float heading_obs = 19;

    optional bool shape_score = 20;

    repeated uint32 lane_id = 21;
    repeated uint32 road_id = 22;

    optional bool is_shape_valid = 23;
}


message TrafficLight {
    required robosense.std_msgs.Header header = 1;
    repeated FrameFusedLight frame_fused_lights = 2;
    optional apollo.core_planning.PlanningStatus planning_status = 3;
}
