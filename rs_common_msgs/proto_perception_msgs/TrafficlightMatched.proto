syntax = "proto2";

package robosense.rs_perception.trafficlight;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBoxMatched.proto";



message TrafficlightMatched {
  // 此header为真实时间戳，可用于计算系统时延
  required robosense.std_msgs.Header header=1; 
  required CameraID camera_id = 2;
  repeated TrafficlightBoxMatched matched_trafficlights = 3;
  required bool contain_hdmap_lights = 4;
  // 此header透传自检测header-->图像header,使用此header对齐时间戳后，比较真实时间戳，可测算match延时
  required robosense.std_msgs.Header related_det_header=5;
}

message BatchTrafficlightMatched {
  // 一个或多个(目前两个)相机的matched_msg
  repeated TrafficlightMatched matched_cams = 1;
}