syntax = "proto2";

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";
import "modules/common_msgs/perception_msgs/Vec3D.proto";

message TrafficlightInstance {
  optional double sys_timestamp = 1;
  optional double det_timestamp = 2;
  optional int32 x = 3;
  optional int32 y = 4;
  optional int32 width = 5;
  optional int32 height = 6;
  optional robosense.rs_perception.trafficlight.NaviType navi_type = 7;
  optional robosense.rs_perception.trafficlight.Color color_state = 8;
  optional double color_score = 9;
  optional robosense.rs_perception.trafficlight.Shape shape = 10;
  optional double shape_score = 11;
  optional bool mainlight = 12;
  optional double mainlight_score = 13;
  optional bool occlusion = 14;
  optional double occlusion_score = 15;
  optional int32 countdown = 16;
  optional string id = 17;
  optional int32 match_x_pix = 18;
  optional int32 match_y_pix = 19;

  optional bool is_exist = 20;
}

message TrafficlightBoxFused {
  required bool is_exist = 1;
  optional robosense.rs_perception.trafficlight.Color color_state = 2;
  optional double color_score = 3;
  optional robosense.rs_perception.trafficlight.NaviType navi_type = 4;
  optional string map_id = 5;
  optional robosense.rs_perception.trafficlight.Source source = 6;
  optional int32 countdown = 7;
  optional robosense.rs_perception.trafficlight.CameraID camera_id = 8;
  repeated TrafficlightInstance match_tl_buffer = 9;
  optional bool occlusion = 10;

  optional robosense.rs_perception.trafficlight.GREEN_FLASH_TIME_STATE green_flash_time_state = 11;
  optional double green_flash_time = 12;
  optional robosense.rs_perception.trafficlight.YELLOW_TIME_STATE yellow_time_state = 13;
  optional double yellow_time = 14;

  // -----------------TODO Del--------------------------
  // Traffic light string-ID in the map data.
  optional string id = 15;

  // Is traffic blinking
  optional bool blink = 16;
    
  // yellow color is from green, and yellow time will be useful
  optional bool green2yellow = 17;

  // the time from green to yellow, default 0
  optional double green2yellow_time = 18;

  optional robosense.perception_msgs.Vec3D center_odom = 19;
}

message MatchedGroup {
  repeated TrafficlightInstance map_instance = 1;
  required int32 x0 = 2;
  required int32 y0 = 3;
  required int32 x1 = 4;
  required int32 y1 = 5;
}

message MatchedFrame {
  repeated MatchedGroup match_groups = 1;
  required double frame_timestamp = 2;
  repeated TrafficlightInstance det_instances = 3;
}

message TrafficlightFused {
  // 此header为当前系统时间，用于测算延时
  required robosense.std_msgs.Header header = 1;
  required TrafficlightBoxFused forward_tl = 2;
  required TrafficlightBoxFused lturn_tl = 3;
  required TrafficlightBoxFused rturn_tl = 4;
  required TrafficlightBoxFused uturn_tl = 5;
  // 此header透传自匹配header-->检测header-->图像header,使用此header对齐时间戳后，比较真实时间戳，可测算端对端延时
  optional double related_match_ts = 6;

  optional MatchedFrame narrow_match_frame = 7;
  optional MatchedFrame wide_match_frame = 8;
  optional bool narrow_match_exist = 9;
  optional bool wide_match_exist = 10;
}
