syntax = "proto2";

package robosense.rs_perception.trafficlight;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBox2D.proto";


message TrafficlightDetection {
  required robosense.std_msgs.Header header=1;
  required CameraID camera_id = 2;
  repeated TrafficlightBox2D trafficlights = 3;
  required bool contain_lights = 4;
  optional int32 crop_x1 = 5;
  optional int32 crop_y1 = 6;
  optional int32 crop_x2 = 7;
  optional int32 crop_y2 = 8;
  optional int32 full_x1 = 9;
  optional int32 full_y1 = 10;
  optional int32 full_x2 = 11;
  optional int32 full_y2 = 12;
}


message BatchTrafficlightDetection {
  repeated TrafficlightDetection trafficlight_detection = 1;
  required robosense.std_msgs.Header header=2;
}