syntax = "proto2";

package robosense.rs_perception.trafficlight;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBox2D.proto";
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightBox3D.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";

message TrafficlightMapTL {
  required string map_id = 1;
  required NaviType navi_type = 2;
  optional double distance = 3;
  required TrafficlightBox2D narrow_map_tl = 4;
  required TrafficlightBox2D wide_map_tl = 5;
  repeated CornerPointInEgo tl_corners = 6;
  required string area_id = 7;
  optional int32 group_id = 8;
}

message TrafficlightMapFrame {
  // 此header为系统时间戳
  required robosense.std_msgs.Header header = 1;
  repeated TrafficlightMapTL map_tls = 2;

  // 此header为定位时间戳(可能为离线录制包，时间戳与系统差距很大)
  required robosense.std_msgs.Header loc_header = 3;

  required int32 tfl_group_num = 4;
}