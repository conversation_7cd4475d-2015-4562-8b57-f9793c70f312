
syntax = "proto2";  

package robosense.perception_msgs;

import "modules/common_msgs/perception_msgs/Vec3D.proto";
import "modules/common_msgs/perception_msgs/Vec2D.proto";
import "modules/common_msgs/perception_msgs/Size3D.proto";
import "modules/common_msgs/perception_msgs/BBox2D.proto";
import "modules/common_msgs/perception_msgs/MotionType.proto";
import "modules/common_msgs/perception_msgs/ObjectType.proto";

import "modules/rs_common_msgs/proto_perception_msgs/ProjectBox.proto";

message ObjectInner
{
    optional uint32 object_id=1;
    optional robosense.perception_msgs.ObjectType type=2;
    optional float type_confidence=3;
    optional robosense.perception_msgs.BBox2D box_full=4;
    optional robosense.perception_msgs.BBox2D box_visible=5;
    optional robosense.perception_msgs.BBox2D left_door_box=6;
    optional float left_door_score=7;
    optional robosense.perception_msgs.BBox2D right_door_box=8;
    optional float right_door_score=9;
    optional robosense.perception_msgs.Vec3D box_center_base=10;
    optional float yaw_base=11;
    optional robosense.perception_msgs.Size3D box_size=12;
    optional float yaw_rate_base=13;
    optional robosense.perception_msgs.Vec3D velocity_base=14;
    optional robosense.perception_msgs.Vec3D acceleration_base=15;
    optional robosense.perception_msgs.MotionType motion_type=16;
    optional uint32 nn_state=17;
    optional uint32 lidar_camera_check=18;
    optional uint32 is_box_refine=19;
    optional uint32 signal_light=20;
    optional uint32 brake_light=21;
    repeated float cutin_states=22;
    optional uint32 door_state=23;
    repeated float reid=24;
    repeated robosense.perception_msgs.Vec2D polygon_base=25;
    repeated robosense.perception_msgs.ProjectBox project_boxes=26;
    optional robosense.perception_msgs.Vec3D pv_proj_point_base=27;
    repeated uint32 extended_key=28;
    repeated float extended_value=29;
}