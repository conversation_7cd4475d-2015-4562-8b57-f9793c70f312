
syntax = "proto2";  

package robosense.perception_msgs;

enum SensorId {
    INVALID_SENSOR_ID = 0;

    CAM_FRONT_NARROW = 1; 
    CAM_FRONT_WIDE = 2; 
    CAM_LEFT_FRONT = 3;
    CAM_LEFT_BACK = 4; 
    CAM_RIGHT_BACK = 5; 
    CAM_RIGHT_FRONT = 6; 
    CAM_BACK = 7; 
    CAM_FISHEYE_FRONT = 10; 
    CAM_FISHEYE_LEFT = 11; 
    CAM_FISHEYE_RIGHT = 12; 
    CAM_FISHEYE_BACK = 13; 

    LIDAR_RUBY_TOP = 20;
    LIDAR_M1_FRONT = 21;
    LIDAR_M1_LEFT = 22;
    LIDAR_M1_RIGHT = 23;
    LIDAR_M1_BACK = 24;
    LIDAR_E1_LEFT = 25;
    LIDAR_E1_RIGHT = 26;
    LIDAR_E1_FRONT = 27;
    LIDAR_E1_BACK = 28;
    LIDAR_EM4_FRONT =29;


    RADAR_FRONT_MRR = 30;
    RADAR_LEFT_BACK_SRR = 31;
    RADAR_RIGHT_BACK_SRR = 32;
    RADAR_BACK_MRR = 33;

    VIRTUAL_CAMERA = 40;
    VIRTUAL_CAMERA_EXTENDED = 41;
}
