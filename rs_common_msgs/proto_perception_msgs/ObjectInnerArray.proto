syntax = "proto2";  

package robosense.perception_msgs;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_perception_msgs/ObjectInner.proto";
import "modules/rs_common_msgs/proto_perception_msgs/Curb.proto";

message ObjectInnerArray
{
    optional robosense.std_msgs.Header header=1;
    optional uint32 sensor_id=2;
    repeated robosense.perception_msgs.ObjectInner object_list=3;
    repeated robosense.perception_msgs.Curb curb_set=4;
}