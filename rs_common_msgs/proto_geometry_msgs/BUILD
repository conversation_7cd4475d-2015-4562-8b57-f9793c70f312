## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "Vector3Stamped_proto",
    srcs = ["Vector3Stamped.proto"],
    deps = [
        ":Vector3_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Transform_proto",
    srcs = ["Transform.proto"],
    deps = [
        ":Quaternion_proto",
        ":Vector3_proto",
    ],
)

proto_library(
    name = "Point32_proto",
    srcs = ["Point32.proto"],
)

proto_library(
    name = "Twist_proto",
    srcs = ["Twist.proto"],
    deps = [
        ":Vector3_proto",
    ],
)

proto_library(
    name = "Inertia_proto",
    srcs = ["Inertia.proto"],
    deps = [
        ":Vector3_proto",
    ],
)

proto_library(
    name = "QuaternionStamped_proto",
    srcs = ["QuaternionStamped.proto"],
    deps = [
        ":Quaternion_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "PoseArray_proto",
    srcs = ["PoseArray.proto"],
    deps = [
        ":Pose_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Polygon_proto",
    srcs = ["Polygon.proto"],
    deps = [
        ":Point32_proto",
    ],
)

proto_library(
    name = "TwistWithCovariance_proto",
    srcs = ["TwistWithCovariance.proto"],
    deps = [
        ":Twist_proto",
    ],
)

proto_library(
    name = "AccelWithCovarianceStamped_proto",
    srcs = ["AccelWithCovarianceStamped.proto"],
    deps = [
        ":AccelWithCovariance_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "InertiaStamped_proto",
    srcs = ["InertiaStamped.proto"],
    deps = [
        ":Inertia_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "PoseWithCovariance_proto",
    srcs = ["PoseWithCovariance.proto"],
    deps = [
        ":Pose_proto",
    ],
)

proto_library(
    name = "AccelWithCovariance_proto",
    srcs = ["AccelWithCovariance.proto"],
    deps = [
        ":Accel_proto",
    ],
)

proto_library(
    name = "TransformStamped_proto",
    srcs = ["TransformStamped.proto"],
    deps = [
        ":Transform_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Point_proto",
    srcs = ["Point.proto"],
)

proto_library(
    name = "TwistStamped_proto",
    srcs = ["TwistStamped.proto"],
    deps = [
        ":Twist_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "PoseStamped_proto",
    srcs = ["PoseStamped.proto"],
    deps = [
        ":Pose_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Vector3_proto",
    srcs = ["Vector3.proto"],
)

proto_library(
    name = "PolygonStamped_proto",
    srcs = ["PolygonStamped.proto"],
    deps = [
        ":Polygon_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Pose2D_proto",
    srcs = ["Pose2D.proto"],
)

proto_library(
    name = "AccelStamped_proto",
    srcs = ["AccelStamped.proto"],
    deps = [
        ":Accel_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Quaternion_proto",
    srcs = ["Quaternion.proto"],
)

proto_library(
    name = "PointStamped_proto",
    srcs = ["PointStamped.proto"],
    deps = [
        ":Point_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Wrench_proto",
    srcs = ["Wrench.proto"],
    deps = [
        ":Vector3_proto",
    ],
)

proto_library(
    name = "WrenchStamped_proto",
    srcs = ["WrenchStamped.proto"],
    deps = [
        ":Wrench_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "PoseWithCovarianceStamped_proto",
    srcs = ["PoseWithCovarianceStamped.proto"],
    deps = [
        ":PoseWithCovariance_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "Accel_proto",
    srcs = ["Accel.proto"],
    deps = [
        ":Vector3_proto",
    ],
)

proto_library(
    name = "Pose_proto",
    srcs = ["Pose.proto"],
    deps = [
        ":Point_proto",
        ":Quaternion_proto",
    ],
)

proto_library(
    name = "TwistWithCovarianceStamped_proto",
    srcs = ["TwistWithCovarianceStamped.proto"],
    deps = [
        ":TwistWithCovariance_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

apollo_package()
