syntax = "proto2";  
package robosense.gazebo_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto"; 
import "modules/rs_common_msgs/proto_geometry_msgs/Twist.proto"; 
import "modules/rs_common_msgs/proto_geometry_msgs/Wrench.proto"; 

message WorldState
{
    required robosense.std_msgs.Header header = 1;
    repeated string name = 2;
    repeated robosense.geometry_msgs.Pose pose = 3;
    repeated robosense.geometry_msgs.Twist twist = 4;
    repeated robosense.geometry_msgs.Wrench wrench = 5;
}
