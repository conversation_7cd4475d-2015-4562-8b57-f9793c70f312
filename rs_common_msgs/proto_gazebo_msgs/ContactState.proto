syntax = "proto2";  
package robosense.gazebo_msgs;
import "modules/rs_common_msgs/proto_geometry_msgs/Wrench.proto"; 
import "modules/rs_common_msgs/proto_geometry_msgs/Vector3.proto";  

message ContactState 
{
    required string info = 1;
    required string collision1_name = 2;
    required string collision2_name = 3;
    repeated robosense.geometry_msgs.Wrench wrenches = 4;
    required robosense.geometry_msgs.Wrench total_wrench = 5;
    repeated robosense.geometry_msgs.Vector3 contact_positions = 6;
    repeated robosense.geometry_msgs.Vector3 contact_normals = 7;
    repeated double depths =  8;
}
