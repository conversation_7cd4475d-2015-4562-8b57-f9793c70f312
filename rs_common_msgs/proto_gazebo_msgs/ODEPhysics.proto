syntax = "proto2";  
package robosense.gazebo_msgs;

message ODEPhysics
{
    required bool auto_disable_bodies = 1;
    required uint32 sor_pgs_precon_iters = 2;
    required uint32 sor_pgs_iters = 3;
    required double sor_pgs_w = 4;
    required double sor_pgs_rms_error_tol = 5;
    required double contact_surface_layer = 6;
    required double contact_max_correcting_vel = 7;
    required double cfm = 8;
    required double erp = 9;
    required uint32 max_contacts = 10;
}
