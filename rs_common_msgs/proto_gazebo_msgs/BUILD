## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "ODEJointProperties_proto",
    srcs = ["ODEJointProperties.proto"],
)

proto_library(
    name = "LinkState_proto",
    srcs = ["LinkState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Twist_proto",
    ],
)

proto_library(
    name = "ModelState_proto",
    srcs = ["ModelState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Twist_proto",
    ],
)

proto_library(
    name = "ContactState_proto",
    srcs = ["ContactState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Vector3_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Wrench_proto",
    ],
)

proto_library(
    name = "ModelStates_proto",
    srcs = ["ModelStates.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Twist_proto",
    ],
)

proto_library(
    name = "SensorPerformanceMetric_proto",
    srcs = ["SensorPerformanceMetric.proto"],
)

proto_library(
    name = "PerformanceMetrics_proto",
    srcs = ["PerformanceMetrics.proto"],
    deps = [
        ":SensorPerformanceMetric_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "WorldState_proto",
    srcs = ["WorldState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Twist_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Wrench_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "ODEPhysics_proto",
    srcs = ["ODEPhysics.proto"],
)

proto_library(
    name = "LinkStates_proto",
    srcs = ["LinkStates.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Twist_proto",
    ],
)

proto_library(
    name = "ContactsState_proto",
    srcs = ["ContactsState.proto"],
    deps = [
        ":ContactState_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

apollo_package()
