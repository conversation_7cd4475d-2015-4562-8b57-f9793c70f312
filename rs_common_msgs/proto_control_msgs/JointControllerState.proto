syntax = "proto2";  
package robosense.control_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 

message JointControllerState
{
    required robosense.std_msgs.Header header = 1;
    required double set_point = 2;
    required double process_value = 3;
    required double process_value_dot = 4;
    required double error = 5;
    required double time_step = 6;
    required double command = 7;

    required double p = 8;
    required double i = 9;
    required double d = 10;
    required double i_clamp = 11;
    required bool antiwindup = 12;
}
