syntax = "proto2";  
package robosense.control_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 
import "modules/rs_common_msgs/proto_basic_msgs/duration.proto"; 

message PidState 
{
    required robosense.std_msgs.Header header = 1;
    required robosense.basic_msgs.duration timestep = 2;
    required double error = 3;
    required double error_dot = 4;
    required double p_error = 5;
    required double i_error = 6;
    required double d_error = 7;
    required double p_term = 8;
    required double i_term = 9;
    required double d_term = 10;
    required double i_max = 11;
    required double i_min = 12;
    required double output = 13;
}
