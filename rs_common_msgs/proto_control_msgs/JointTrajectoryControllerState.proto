syntax = "proto2";  
package robosense.control_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 
import "modules/rs_common_msgs/proto_trajectory_msgs/JointTrajectoryPoint.proto"; 

message JointTrajectoryControllerState 
{
    required robosense.std_msgs.Header header = 1;
    repeated string joint_names = 2;
    required robosense.trajectory_msgs.JointTrajectoryPoint desired = 3;
    required robosense.trajectory_msgs.JointTrajectoryPoint actual = 4;
    required robosense.trajectory_msgs.JointTrajectoryPoint error = 5;
}
