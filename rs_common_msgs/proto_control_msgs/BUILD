## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "GripperCommand_proto",
    srcs = ["GripperCommand.proto"],
)

proto_library(
    name = "JointTrajectoryControllerState_proto",
    srcs = ["JointTrajectoryControllerState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
        "//modules/rs_common_msgs/proto_trajectory_msgs:JointTrajectoryPoint_proto",
    ],
)

proto_library(
    name = "JointJog_proto",
    srcs = ["JointJog.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "PidState_proto",
    srcs = ["PidState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_basic_msgs:duration_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "JointControllerState_proto",
    srcs = ["JointControllerState.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "JointTolerance_proto",
    srcs = ["JointTolerance.proto"],
)

apollo_package()
