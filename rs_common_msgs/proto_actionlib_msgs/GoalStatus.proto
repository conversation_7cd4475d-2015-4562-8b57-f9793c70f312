syntax = "proto2";
package robosense.actionlib_msgs;

import "modules/rs_common_msgs/proto_actionlib_msgs/GoalID.proto";

message GoalStatus
{

    enum STATUS_TYPE 
    {
        PENDING = 0; 
        ACTIVE = 1; 
        PREEMPTED = 2; 
        SUCCESSED = 3; 
        ABORTED = 4; 
        REJECTED = 5; 
        PREEMPTING = 6; 
        RECALLING = 7; 
        RECALLED = 8; 
        LOST = 9; 
    }

    required robosense.actionlib_msgs.GoalID goal_id = 1;
    required STATUS_TYPE status = 2;
    required string text = 3;
}
