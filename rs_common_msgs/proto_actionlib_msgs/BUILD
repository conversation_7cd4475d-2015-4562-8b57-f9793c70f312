## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "GoalStatusArray_proto",
    srcs = ["GoalStatusArray.proto"],
    deps = [
        ":GoalStatus_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "GoalID_proto",
    srcs = ["GoalID.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_basic_msgs:time_proto",
    ],
)

proto_library(
    name = "GoalStatus_proto",
    srcs = ["GoalStatus.proto"],
    deps = [
        ":GoalID_proto",
    ],
)

apollo_package()
