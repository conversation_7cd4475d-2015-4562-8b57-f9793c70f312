syntax = "proto2";

package robosense.prefabricated_routes_msgs; 

message PrefabricatedRoutes {
    optional bool is_enable              = 1[default = false]; 
    optional bool is_valid               = 2[default = false]; 
    repeated string prefabricated_routes = 3; 
    optional string error_info           = 4;
}

message GetNavigateModeSwitch {
    optional uint32   seq       = 1; 
    optional uint64   timestamp = 2; 
}

message NavigateModeSwitch {
    optional uint64    timestamp                   = 1; 
    optional string    lanemap_mapfree_switch_mode = 2[default = "lanemap"]; 
    optional bool      lanemap_use_switch_mode     = 3[default = true]; 
}