syntax = "proto2"; 

package robosense.quick_data_msgs;

enum RS_QUICK_DATA_REQ_OP_TYPE 
{
    RS_QUICK_DATA_REQ_UPLOAD = 1; 
    RS_QUICK_DATA_REQ_CANCLE = 2;
}

message RSQuickDataRequest 
{
    optional uint64 request_timestamp = 1; 
    optional uint64 request_id        = 2; 
    // QuickData 相关的url
    optional string issue_url         = 3; 
    optional string issue_log_url     = 4; 
    // 行程开始/结束的log 的命令
    optional string start_log_url     = 5; 
    optional string end_log_url       = 6; 
    optional RS_QUICK_DATA_REQ_OP_TYPE req_op_type = 7[default = RS_QUICK_DATA_REQ_UPLOAD]; 
}

message RSQuickDataResponse 
{
    optional uint64 response_timestamp = 1; 
    optional uint64 request_id = 2; 
    optional int32  error_code = 3[default = 0]; 
    optional string error_info = 4[default = ""]; 
}

message RSQuickDataProgress 
{
    optional uint64 response_timestamp = 1; 
    optional uint64 request_id         = 2; 
    optional uint32 progress           = 3;  
}

enum RS_QUICK_DATA_LOG_OP_TYPE { 
    RS_QUICK_DATA_OP_QUICKDATA_LOG_A = 1; 
    RS_QUICK_DATA_OP_QUICKDATA_LOG_B = 2; 
}

enum RS_TRIP_LOG_OP_TYPE { 
    RS_TRIP_LOG_OP_QUICKDATA = 1; 
    RS_TRIP_LOG_OP_START     = 2; 
    RS_TRIP_LOG_OP_END       = 3; 
}

message RSQuickDataLogRequest 
{
    optional uint64 request_timestamp        = 1; 
    optional uint64 request_id               = 2; 
    optional RS_TRIP_LOG_OP_TYPE log_op_type = 3; 
    optional string log_url                  = 4;   
    optional uint32 quickdata_log_before_ms  = 5; 
    optional uint32 quickdata_log_after_ms   = 6; 
    optional RS_QUICK_DATA_REQ_OP_TYPE req_op_type = 7[default = RS_QUICK_DATA_REQ_UPLOAD]; 
}

message RSQuickDataLogResponse 
{
    optional uint64 response_timestamp = 1; 
    optional uint64 request_id = 2; 
    optional int32  error_code = 3[default = 0]; 
    optional string error_info = 4[default = ""];
    optional RS_QUICK_DATA_LOG_OP_TYPE op_type = 5; 
}

message RSQuickDataLogProgress 
{
    optional uint64 response_timestamp = 1; 
    optional uint64 request_id         = 2; 
    optional uint32 progress           = 3;  
    optional RS_QUICK_DATA_LOG_OP_TYPE op_type = 4;  
}