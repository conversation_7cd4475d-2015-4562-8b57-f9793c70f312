syntax = "proto2";  
package robosense.rs_version; 

message RSVersionMessage {
    optional string version = 1; 
    optional string candidate_carapp_version = 2; 
    optional string map_file = 3;
    optional string map_mode = 4;
    optional string loc_map_version = 5;
    optional string lane_map_version = 6;
    optional string mapnode_type = 7; 
    optional string behavior_map_version = 8; 
}

message RSVersionSwitch {
    optional bool enable_version = 1[default = true]; 
}
