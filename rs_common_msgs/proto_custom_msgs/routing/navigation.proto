syntax = "proto2";

package robosense.routing;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto"; 

message Position {
    optional double lat = 1;
    optional double lon = 2;
}

message Navigation {
    optional robosense.std_msgs.Header header = 1; 
    optional Position position = 2;
    optional Position branch = 3;
    optional double branch_dist = 4;

    enum BranchAction {
        BRANCH_STRAIGHT = 0;
        BRANCH_LEFT_TRUN = 1;
        BRANCH_RIGHT_TURN = 2;
    }
    optional BranchAction branch_action = 5;
    optional int32 lane_num = 6;

    enum LaneAction {
        LANE_STRAIGHT = 0;
        Lane_LEFT_TRUN = 1;
        Lane_STRAIGHT_OR_LEFT_TURN = 2;
        Lane_RIGHT_TURN = 3;
    }
    repeated LaneAction lane_action = 7;
    repeated int32 lane_divable = 8;
    optional double speed_limit = 9;
    optional double congestion_dist = 10;
}