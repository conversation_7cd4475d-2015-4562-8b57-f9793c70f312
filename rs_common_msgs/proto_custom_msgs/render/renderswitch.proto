syntax = "proto2";

package robosense.render_msgs; 

enum RS_RENDER_TASK_MAP_SOURCE_TYPE {
    RS_RENDER_TASK_MAP_FROM_FILE = 1; 
    RS_RENDER_TASK_MAP_ROAD_STRUCTURE = 2; 
    RS_RENDER_TASK_MAP_BOTH = 3; 
}

enum RS_CARAPP_MODE {
    RS_DEBUG = 1;
    RS_USER = 2;
}

enum RS_WORLD_MODEL_MODE {
    RS_PILOT = 1;
    RS_PERCEPTION = 2;
}

enum RS_RENDER_COORDINATE {
    RS_BASE = 1;  // 自车坐标系
    RS_ODOM = 2;  // odom坐标系
}

enum RS_RENDER_COMMUNICATION_TYPE {
    RS_RENDER_COMM_WEBSOCKET            = 1; 
    RS_RENDER_COMM_UDP_MULTICAST        = 2; 
    RS_RENDER_COMM_DOUBLE_UDP_MULTICAST = 3; 
    RS_RENDER_COMM_UDP_P2P              = 4; 
}

enum RS_UDP_CONTROL_MODE_TYPE {
    RS_UDP_CONTROL_NOTHING            = 1; 
    RS_UDP_CONTROL_TOTAL_CONTROL_TIME = 2; 
    RS_UDP_CONTROL_DATA_CONTROL_TIME  = 3; 
}

enum RS_RENDER_OBJECT_SOURCE_TYPE {
    RS_OBJECT_SOURCE_PREDICTION = 1; 
    RS_OBJECT_SOURCE_DYNAMICOBJECT = 2; 
}

message UdpControlConfig {
    optional RS_UDP_CONTROL_MODE_TYPE udp_control_type                 = 1[default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME]; 
    optional uint32                   udp_total_control_time_ms        = 2[default = 60]; 
    optional uint32                   udp_total_control_single_time_ms = 3[default = 2]; 
    optional uint32                   udp_data_control_size            = 4[default = 262144]; 
    optional uint32                   udp_data_control_time_ms         = 5[default = 2]; 
}

enum RS_UDP_BUFFER_CONTROL_MODE_TYPE {
    RS_UDP_BUFFER_CONTROL_NOTHING = 1; 
    RS_UDP_BUFFER_CONTROL_ENABLE  = 2; 
}

message UdpBufferControlConfig {
    optional RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type       = 1[default = RS_UDP_BUFFER_CONTROL_ENABLE]; 
    optional uint32                          udp_dynamic_single_control_th = 2[default = 3]; 
    optional uint32                          udp_static_single_control_th  = 3[default = 3];
    optional uint32                          udp_combine_control_th        = 4[default = 6]; 
    optional uint32                          udp_buffer_control_send_frame_gap = 5[default = 2];  
}

message RenderMapConfig 
{
    optional RS_RENDER_TASK_MAP_SOURCE_TYPE map_source_type = 1[default = RS_RENDER_TASK_MAP_FROM_FILE]; 
    optional double map_from_file_local_range_width = 2 [default = 100]; 
    optional double map_from_file_local_range_height = 3 [default = 300];
}

message RenderMapCropConfig 
{
    optional bool enable_map_crop = 1[default = true]; 
    optional double bottom_x      = 2[default = -20]; 
    optional double top_x         = 3[default = 130]; 
    optional double bottom_y      = 4[default = -32]; 
    optional double top_y         = 5[default = 32]; 
}

message RenderSwitch 
{
    optional bool enable_render                         = 1[default = true]; 
    optional RenderMapConfig render_map_config          = 2; 
    optional RenderMapCropConfig render_map_crop_config = 3; 
    optional RS_CARAPP_MODE carapp_mode                 = 4[default = RS_DEBUG];
    optional bool enable_websocket_debug                = 5[default = false]; 
    optional uint32 static_message_interval             = 6[default = 2];
    optional RS_WORLD_MODEL_MODE world_model_mode       = 7[default = RS_PERCEPTION];
    optional RS_RENDER_COORDINATE render_coordinate     = 8[default = RS_BASE];
    optional RS_RENDER_COMMUNICATION_TYPE render_comm_type  = 9[default = RS_RENDER_COMM_WEBSOCKET]; 
    optional string                       udp_p2p_carapp_ip = 10; 
    optional bool                         enable_world_model_mode = 11; 
    optional bool                         enable_message_timeout_check = 12; 
    optional uint32                       message_timeout_th_ms = 13; 
    optional UdpControlConfig             udp_control_config = 14; 
    optional RS_RENDER_OBJECT_SOURCE_TYPE render_object_source = 15; 
    optional UdpBufferControlConfig       udp_multicast_buffer_control_config = 16; 
    optional bool                         enable_nav_2km = 17[default = false];
    optional bool                         enable_obstacle_high_light = 18[default = false]; 
}
