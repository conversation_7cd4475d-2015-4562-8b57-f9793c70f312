syntax = "proto2";

package robosense.render_msgs; 

import "modules/rs_common_msgs/proto_perception_msgs/ObjectFusionArray.proto"; 
import "modules/rs_common_msgs/proto_world_model_msgs/RoadStructure.proto"; 
import "modules/common_msgs/localization_msgs/localization.proto"; 
import "modules/common_msgs/planning_msgs/planning.proto"; 
import "modules/common_msgs/control_msgs/control_cmd.proto";
import "modules/common_msgs/perception_msgs/traffic_light_detection.proto"; 
import "modules/common_msgs/prediction_msgs/prediction_obstacle.proto"; 
import "modules/common_msgs/map_msgs/map.proto"; 
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightFused.proto"; 
import "modules/rs_common_msgs/proto_perception_msgs/TrafficlightPost.proto";
import "modules/rs_common_msgs/proto_perception_msgs/OccupancyMap.proto"; 
import "modules/rs_common_msgs/proto_custom_msgs/collect/rscompressedimage.proto"; 
import "modules/rs_common_msgs/proto_sensor_msgs/CompressedImage.proto"; 
import "modules/common_msgs/chassis_msgs/chassis.proto"; 
import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap.proto"; 
import "modules/rs_common_msgs/proto_custom_msgs/time_delay/time_delay.proto";
import "modules/common_msgs/localization_msgs/lane_pair.proto"; 
import "modules/rs_common_msgs/proto_nav_msgs/navi_route.proto"; 

message Render {
    optional robosense.perception_msgs.ObjectFusionArray   object_fusion_array           = 1;  
    optional uint64                                        object_fusion_array_timestamp = 2;
    optional uint64                                        object_fusion_array_size      = 3;

    optional robosense.perception.worldmodel.RoadStructure road_structure           = 4; 
    optional uint64                                        road_structure_timestamp = 5;
    optional uint64                                        road_structure_size      = 6;

    optional apollo.localization.LocalizationEstimate      localization           = 7; 
    optional uint64                                        localization_timestamp = 8;
    optional uint64                                        localization_size      = 9;

    optional apollo.planning.ADCTrajectory                 adctrajectory           = 10; 
    optional uint64                                        adctrajectory_timestamp = 11;
    optional uint64                                        adctrajectory_size      = 12;

    optional apollo.perception.TrafficLightDetection       traffic_light_dection           = 13; 
    optional uint64                                        traffic_light_dection_timestamp = 14;
    optional uint64                                        traffic_light_dection_size      = 15;

    optional apollo.hdmap.Map                              hdmap_map           = 16; 
    optional uint64                                        hdmap_map_timestamp = 17;
    optional uint64                                        hdmap_map_size      = 18;

    optional robosense.perception_msgs.TrafficlightFused   trafficlight_fused           = 19; 
    optional uint64                                        trafficlight_fused_timestamp = 20;
    optional uint64                                        trafficlight_fused_size      = 21;

    optional apollo.prediction.PredictionObstacles         prediction_obstacles           = 22; 
    optional uint64                                        prediction_obstacles_timestamp = 23;
    optional uint64                                        prediction_obstacles_size      = 24;

    optional robosense.perception_msgs.OccupancyMap        occupance_map           = 25; 
    optional uint64                                        occupance_map_timestamp = 26;
    optional uint64                                        occupance_map_size      = 27;

    optional robosense.rscamera_msg.RsCompressedImage      camera_h265           = 28; 
    optional uint64                                        camera_h265_timestamp = 29;
    optional uint64                                        camera_h265_size      = 30;

    optional robosense.sensor_msgs.CompressedImage         camera_jpeg           = 31; 
    optional uint64                                        camera_jpeg_timestamp = 32;
    optional uint64                                        camera_jpeg_size      = 33;

    optional apollo.a_canbus.Chassis                       adas_chassis           = 34; 
    optional uint64                                        adas_chassis_timestamp = 35;
    optional uint64                                        adas_chassis_size      = 36;

    optional robosense.rs_map.lanemap.LaneMap              lanemap               = 37; 
    optional uint64                                        lanemap_timestamp     = 38;
    optional uint64                                        lanemap_size          = 39;

    optional robosense.time_delay_msgs.time_delay          time_delay            = 40;
    optional uint64                                        time_delay_timestamp  = 41;
    optional uint64                                        time_delay_size       = 42;

    optional uint64 begin_time              = 43;
    optional uint64 end_time                = 44;
    optional uint64 send_hmi_time           = 45;
    optional uint64 recv_hmi_time           = 46;
    optional uint64 deserialize_hmi_time    = 47;
    optional uint64 deserialize_render_time = 48;
    optional uint64 render_time             = 49;
    optional uint64 seq_number              = 50[default = 0];

    optional apollo.control.ControlCommand control_command           = 51;
    optional uint64                        control_command_timestamp = 52;
    optional uint64                        control_command_size      = 53;

    optional robosense.perception_msgs.TrafficLight traffic_light           = 54;
    optional uint64                                 traffic_light_timestamp = 55; 
    optional uint64                                 traffic_light_size      = 56; 

    optional rs_lane_localization.RoadIdList        road_id = 57; 
    optional uint64                                 road_id_timestamp = 58; 
    optional uint64                                 road_id_size = 59; 
    
    optional robosense.rs_map.com_msgs.navimsgs.NaviRoute navi_route = 60; 
    optional uint64                                       navi_route_timestamp = 61; 
    optional uint64                                       navi_route_size = 62; 
}
