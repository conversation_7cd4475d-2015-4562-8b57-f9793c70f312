## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "renderswitch_proto",
    srcs = ["renderswitch.proto"],
)

proto_library(
    name = "render_proto",
    srcs = ["render.proto"],
    deps = [
        "//modules/common_msgs/chassis_msgs:chassis_proto",
        "//modules/common_msgs/control_msgs:control_cmd_proto",
        "//modules/common_msgs/localization_msgs:lane_pair_proto",
        "//modules/common_msgs/localization_msgs:localization_proto",
        "//modules/common_msgs/map_msgs:map_proto",
        "//modules/common_msgs/perception_msgs:traffic_light_detection_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "//modules/common_msgs/prediction_msgs:prediction_obstacle_proto",
        "//modules/rs_common_msgs/proto_custom_msgs/collect:rscompressedimage_proto",
        "//modules/rs_common_msgs/proto_custom_msgs/time_delay:time_delay_proto",
        "//modules/rs_common_msgs/proto_lanemap_msgs:lanemap_proto",
        "//modules/rs_common_msgs/proto_nav_msgs:navi_route_proto",
        "//modules/rs_common_msgs/proto_perception_msgs:ObjectFusionArray_proto",
        "//modules/rs_common_msgs/proto_perception_msgs:OccupancyMap_proto",
        "//modules/rs_common_msgs/proto_perception_msgs:TrafficlightFused_proto",
        "//modules/rs_common_msgs/proto_perception_msgs:TrafficlightPost_proto",
        "//modules/rs_common_msgs/proto_sensor_msgs:CompressedImage_proto",
        "//modules/rs_common_msgs/proto_world_model_msgs:RoadStructure_proto",
    ],
)

apollo_package()
