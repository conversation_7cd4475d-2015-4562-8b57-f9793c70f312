syntax = "proto2";

package robosense.common;

message PointENU {
  optional double x = 1 [default = nan];  // East from the origin, in meters.
  optional double y = 2 [default = nan];  // North from the origin, in meters.
  optional double z = 3 [default = 0.0];  // Up from the WGS-84 ellipsoid, in
                                          // meters.
}

// A point in the global reference frame. Similar to PointENU, PointLLH allows
// omitting the height field for representing a 2D location.
message PointLLH {
  // Longitude in degrees, ranging from -180 to 180.
  optional double lon = 1 [default = nan];
  // Latitude in degrees, ranging from -90 to 90.
  optional double lat = 2 [default = nan];
  // WGS-84 ellipsoid height in meters.
  optional double height = 3 [default = 0.0];
}

// A general 2D point. Its meaning and units depend on context, and must be
// explained in comments.
message Point2D {
  optional double x = 1 [default = nan];
  optional double y = 2 [default = nan];
}

// A general 3D point. Its meaning and units depend on context, and must be
// explained in comments.
message Point3D {
  optional double x = 1 [default = nan];
  optional double y = 2 [default = nan];
  optional double z = 3 [default = nan];
}

// A unit quaternion that represents a spatial rotation. See the link below for
// details.
//   https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation
// The scalar part qw can be omitted. In this case, qw should be calculated by
//   qw = sqrt(1 - qx * qx - qy * qy - qz * qz).
message Quaternion {
  optional double qx = 1 [default = nan];
  optional double qy = 2 [default = nan];
  optional double qz = 3 [default = nan];
  optional double qw = 4 [default = nan];
}

// A general polygon, points are counter clockwise
message Polygon {
  repeated Point3D point = 1;
}
