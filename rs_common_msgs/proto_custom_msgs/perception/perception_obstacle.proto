syntax = "proto2";

package robosense.perception;

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_custom_msgs/common/geometry.proto";

message Size {
  // Size of obstacle bounding box.
  optional double length = 1;  // obstacle length.
  optional double width = 2;   // obstacle width.
  optional double height = 3;  // obstacle height.
}

enum PerceptionType {
  TYPE_UNKNOWN = 0;
  TYPE_CAR = 1;
  TYPE_BUS = 2;
  TYPE_TURCK = 3;
  TYPE_CONSTRN_VEH = 4; 
  TYPE_CYC = 5;
  TYPE_TRICYCLE = 6;
  TYPE_PED = 7;
  TYPE_TRAFFIC_CONE = 8;
  TYPE_BARROW = 9;  
  TYPE_ANIMAL = 10;
  TYPE_WARN_TRIANGLE = 11;
  TYPE_BIRD = 12;
  TYPE_WATER_BARRIER = 13;
  TYPE_LAMP_POST = 14; 
  TYPE_TRAFFIC_SIGN = 15; 
  TYPE_WARN_POST = 16;  
  TYPE_TRAFFIC_BARREL = 17; 
  TYPE_ARTICULATED_HEAD = 18;
  TYPE_ARTICULATED_BODY = 19;
  TYPE_VISION_OBSTACLE = 20; 
}

enum MotionType {
  UNKNOWN = 0;
  STATIONARY = 1;
  STOPED = 2;
  MOVING = 3;
}

enum Confidence {
  HIGH = 0;
  MEDIAN = 1;
  LOW = 2;
}

message PerceptionObstacle {
  optional int32 tracker_id = 1;  // obstacle ID.

  optional double yaw = 2;  // orientation in the world coordinate system.

  optional Size size = 3;

  optional robosense.common.Point3D center = 4;

  optional PerceptionType type = 5;         // obstacle type

  required robosense.common.Polygon polygon = 6;  // obstacle corner points.

  optional double heading = 7;

  optional double angular_velocity = 8;

  optional robosense.common.Point3D abs_velocity = 9;  // obstacle velocity.

  optional robosense.common.Point3D abs_acceleration = 10;  // obstacle acceleration

  optional MotionType motion_type = 11;

  // cut in probability in 0s, 1s, 2s, 3s
  repeated double cutin_states = 12;

  optional Confidence exist_confidence = 13;
}

message PerceptionObstacles {
  optional robosense.std_msgs.Header header = 1;             // Header
  repeated PerceptionObstacle perception_obstacle = 2;  // An array of obstacles
}
