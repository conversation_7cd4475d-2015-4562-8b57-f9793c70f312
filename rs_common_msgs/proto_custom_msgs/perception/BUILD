## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "perception_obstacle_proto",
    srcs = ["perception_obstacle.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_custom_msgs/common:geometry_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "road_structure_proto",
    srcs = ["road_structure.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

apollo_package()
