syntax = "proto2"; 

package robosense.remote_control_msgs;

message RSRemoteControlCameraItem{
    optional bool   enable_camera       = 1[default = false]; 
    optional string camera_channel_name = 2; 
}

message RSRemoteControlCameraConfig 
{
    optional uint64                    timestamp_ns   = 1; 
    optional uint64                    sequence       = 2; 
    optional RSRemoteControlCameraItem camera_config  = 3;  
}

enum RS_REMOTE_CONTROL_PNC_OP_TYPE {
    RS_REMOTE_CONTROL_PNC_NOTHING    = 1; 
    RS_REMOTE_CONTROL_PNC_LEFT_TURN  = 2; 
    RS_REMOTE_CONTROL_PNC_RIGHT_TURN = 3; 
}

message RSRemoteControlPncConfig 
{
    optional uint64  timestamp_ns = 1; 
    optional uint64  sequence     = 2; 
    optional RS_REMOTE_CONTROL_PNC_OP_TYPE pnc_type = 3; 
}

message RSRemoteControlAck
{
    optional uint64 timestamp_ns     = 1; 
    optional uint64 sequence         = 2; 
    optional uint64 ack_timestamp_ns = 3; 
    optional int32  error_code       = 4;
    optional string error_info       = 5;  
}

message RSRemoteControlClientInfo
{
    optional uint64 timestamp_ns = 1; 
    optional string client_tag   = 2; 
}