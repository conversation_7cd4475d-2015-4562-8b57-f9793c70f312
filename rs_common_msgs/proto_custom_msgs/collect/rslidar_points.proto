syntax = "proto2";
package robosense.rslidar_msg; 

import "modules/rs_common_msgs/proto_std_msgs/Header.proto";  

message PointXYZIRT {
  optional float x = 1 [default = nan];
  optional float y = 2 [default = nan];
  optional float z = 3 [default = nan];
  optional float intensity = 4 [default = 0];
  optional uint32 ring = 5 [default = 0]; 
  optional uint64 timestamp = 6 [default = 0];
}

message RslidarPoints {
  required robosense.std_msgs.Header header = 1; 
  repeated PointXYZIRT point = 2;
  optional bool   is_dense = 3;  
  optional uint32 width = 4;
  optional uint32 height = 5;
}
