## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "rsrawgnss_msgs_proto",
    srcs = ["rsrawgnss_msgs.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rscan_data_proto",
    srcs = ["rscan_data.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rslidar_points_proto",
    srcs = ["rslidar_points.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rscompressedimage_proto",
    srcs = ["rscompressedimage.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rscan_data_parse_proto",
    srcs = ["rscan_data_parse.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rslidar_scans_proto",
    srcs = ["rslidar_scans.proto"],
    deps = [
        ":rslidar_packet_proto",
    ],
)

proto_library(
    name = "rslidar_packet_proto",
    srcs = ["rslidar_packet.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "rscan_datas_proto",
    srcs = ["rscan_datas.proto"],
    deps = [
        ":rscan_data_proto",
    ],
)

apollo_package()
