syntax = "proto2";  
package robosense.nav_msgs;
import "modules/rs_common_msgs/proto_std_msgs/Header.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/PoseWithCovariance.proto"; 
import "modules/rs_common_msgs/proto_geometry_msgs/TwistWithCovariance.proto"; 

message Odometry
{
    required robosense.std_msgs.Header header = 1;
    required string child_frame_id = 2;
    required robosense.geometry_msgs.PoseWithCovariance pose = 3;
    required robosense.geometry_msgs.TwistWithCovariance twist = 4;
}
