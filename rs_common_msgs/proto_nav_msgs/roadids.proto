syntax = "proto2";

package robosense.rs_map.com_msgs.navimsgs;

import "modules/rs_common_msgs/proto_nav_msgs/common.proto";

message Polyline
{
  repeated PointLLA points= 1;    // 每分段路线上的点, wgs84 for now(20240429)
}

message Polygon
{
  repeated PointLLA points= 1;    // 每分段路线上的点, wgs84 for now(20240429)
}

message SplittingRoads{
  required uint64 parent_road_id_index = 1;
  repeated uint64 child_road_id_index = 2;
}

message RoadId{
  required uint64 id = 1;
  repeated uint64 road_id = 2;
  repeated double road_length = 3;
  repeated Polyline polyline = 4;  // 每段roadid对应的polyline集合
  repeated Polygon polygon = 5;  // 每段roadid对应的polygon集合
  optional double score = 6; // 匹配得分
  optional Polyline navi_points = 7;  // 原始导航轨迹点的集合
  repeated Polyline break_points = 8; // 原始导航轨迹断点集合，每个Polyline理论上有且只有两个点
  repeated SplittingRoads splitting_roads = 9; // 分叉道路
}

message RoadIds{
  repeated RoadId road_ids = 1;
  optional uint64 timestamp = 2;
  optional bool route_status = 3;  // 路线状态，true为新路线，有效；false为旧路线，无效。
}

message NaviPoints
{
  repeated PointLLA points= 1;    // 每分段路线上的点, wgs84 for now(20240429)
  required uint64 timestamp = 2;
}