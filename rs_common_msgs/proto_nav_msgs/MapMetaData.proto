syntax = "proto2";  
package robosense.nav_msgs;
import "modules/rs_common_msgs/proto_basic_msgs/time.proto";
import "modules/rs_common_msgs/proto_geometry_msgs/Pose.proto"; 

message MapMetaData 
{
    required robosense.basic_msgs.time map_load_time = 1;
    required float resolution = 2;
    required uint32 width       = 3;
    required uint32 height      = 4;
    required robosense.geometry_msgs.Pose origin = 5;
}
