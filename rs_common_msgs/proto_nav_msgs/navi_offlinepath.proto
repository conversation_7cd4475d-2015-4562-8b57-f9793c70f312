syntax = "proto2";

package robosense.rs_map.com_msgs.navimsgs;

import "modules/rs_common_msgs/proto_nav_msgs/common.proto";
import "modules/rs_common_msgs/proto_nav_msgs/roadids.proto";

message OfflinePath {
  required uint64 id = 1;
  required string path_name = 2;
  repeated PointLLA trajectory = 3;
  repeated uint64 road_ids = 4;
  repeated double road_lengths = 5;
}

message OfflinePaths {
  required string map_version = 1;     // 对应map版本
  required string data_version = 2;    // path的版本
  repeated OfflinePath path = 3;
  optional RoadIds polypaths = 4;          // path
}
