## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "Odometry_proto",
    srcs = ["Odometry.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:PoseWithCovariance_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:TwistWithCovariance_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "navi_route_proto",
    srcs = ["navi_route.proto"],
    deps = [
        ":common_proto",
    ],
)

proto_library(
    name = "common_proto",
    srcs = ["common.proto"],
)

proto_library(
    name = "GridCells_proto",
    srcs = ["GridCells.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:Point_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "OccupancyGrid_proto",
    srcs = ["OccupancyGrid.proto"],
    deps = [
        ":MapMetaData_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "noa_data_proto",
    srcs = ["noa_data.proto"],
    deps = [
        "//modules/common_msgs/localization_msgs:localization_proto",
    ],
)

proto_library(
    name = "roadids_proto",
    srcs = ["roadids.proto"],
    deps = [
        ":common_proto",
    ],
)

proto_library(
    name = "Path_proto",
    srcs = ["Path.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_geometry_msgs:PoseStamped_proto",
        "//modules/rs_common_msgs/proto_std_msgs:Header_proto",
    ],
)

proto_library(
    name = "navi_offlinepath_proto",
    srcs = ["navi_offlinepath.proto"],
    deps = [
        ":common_proto",
        ":roadids_proto",
    ],
)

proto_library(
    name = "trajectorys_proto",
    srcs = ["trajectorys.proto"],
    deps = [
        ":common_proto",
    ],
)

proto_library(
    name = "MapMetaData_proto",
    srcs = ["MapMetaData.proto"],
    deps = [
        "//modules/rs_common_msgs/proto_basic_msgs:time_proto",
        "//modules/rs_common_msgs/proto_geometry_msgs:Pose_proto",
    ],
)

apollo_package()
