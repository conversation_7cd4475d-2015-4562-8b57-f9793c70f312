syntax = "proto2";

package robosense.rs_map.com_msgs.navimsgs;
import "modules/rs_common_msgs/proto_nav_msgs/common.proto";


message GoalCategories
{
  required uint32 category          = 1;    // 提示类型
  required PointLLA curr_point      = 2;    // TBT位置, gcj02
  optional Point3D curr_point_ego   = 3;    // TBT位置, ego坐标系
  optional Point3D curr_point_odom  = 4;    // TBT位置, odom坐标系
}

message LaneInfos
{
  required uint32 lane_sort_idx = 1;  // lane索引
  required bool passable = 2;         // 是否可通行
  required int32 turn_type = 3;      // 转向类型
}

// 语音播报类型
enum NaviTTSType{
  NAVIINFO_TEXT = 1;      // 导航中引导信息播报
  NAVI_START_TEXT = 2;    // 导航开始信息播报
  NAVI_END_TEXT = 3;      // 导航结束信息播报
  CUSTOM_TTS_TEXT = 4;    // 自定义语音播报
  INTERRUPT_CURRENT = 5;  // 打断当前播报 注：收到该类型时，表明后续会有较高优先级的播报，建议立即打断当前播报，清空播报队列
  NAVI_YAW = 6;           // 导航偏航提示播报
};

enum EventType{
  UNKNOWN = 0;
  LONG_SOLID_LINE = 1; // 长实线
};

message NaviRouteGD
{
  repeated GoalCategories goal_categories = 1; // 目标信息
  required double remain_distance = 2;         // 剩余距离
  repeated LaneInfos lane_infos = 3;           // gd车道信息
  repeated PointLLA navi_points_lla = 4;       // 2km导航路径, gcj02
  required double speed_limit = 5;             // 限速（有限速摄像头的路段才有）
  required uint64 timestamp_nsec = 6;          // 当前时间戳(纳秒）高德语音播报
  repeated Point3D navi_points_ego = 7;        // 2km导航路径, ego坐标系
  repeated Point3D navi_points_odom = 8;       // 2km导航路径, odom坐标系
  optional uint32 cur_road_class = 9;          // 当前road class
  optional uint32 cur_road_type = 10;          // 当前road type
  optional uint32 cur_link_type = 11;          // 当前link type
  optional string navi_tts_text = 12;          // 语音播报的原始文本
  optional NaviTTSType navi_tts_type = 13;     // 语音播报类型
  optional EventType event_type = 14;          // 从文本解析出来的导航事件类型，目前包含“长实线”类型，未来可扩展
  optional float event_dist = 15;              // 事件距离，大概率没有
  optional string cross = 16;                  // 路口放大图
  optional string snapshot = 17;               // 导航快照
}

message Point {
  required double x = 1;
  required double y = 2;
}

enum LaneTurnKind {
  invalid = 0; // 无效
  front   = 1; // 直行
  right   = 2; // 右转
  back    = 3; // 掉头
  left    = 4; // 左转
};

message LaneItem {
  repeated LaneTurnKind turn_view      = 1;     // 车道信息总览: 包含所有车道转向信息。
  required bool is_valid                        = 2;     // 是否可通行：车道转向包含turn_kind
  optional bool is_recommend                    = 3;     // 是否推荐车道: 是否推荐车道，非必要信息
}

message LaneInfo {
  required uint32 add_dist   = 1;   // 距离路线起点的累积距离：米
  required uint64 link_id    = 2;   // link id
  required Point  pos        = 3;   // 位置：gcj02

  required LaneTurnKind turn_kind         = 4;             // 当前add_dist车道转向
  repeated LaneItem lane_item    = 5;             // 车道信息：从左到右排序
}


// TBT
message TbtInfo {
  optional uint64 link_id    = 1;   // TBT位置,所在linkID
  optional Point  pos        = 2;   // TBT位置, gcj02
  optional uint64 index      = 3;   // 所在link_ids中的index
  optional uint32 tbt_type   = 4;   // 转向类型: TbtType
  optional uint32 add_dist   = 5;   // 距离路线起点的累积距离：米。注：目前无此信息
  optional Point3D pos_ego     = 6;   // TBT位置, ego坐标系
  optional Point3D pos_odom    = 7;   // TBT位置, odom坐标系
}

message NaviRouteBD
{
  repeated TbtInfo tbt_info = 1;  // tbt信息
  required double remain_distance = 2;    // 剩余距离
  repeated LaneInfo lane_info = 3;      // bd车道信息。注意，目前无此信息。
  repeated PointLLA navi_points_lla = 4;  // 2km导航路径, gcj02
  optional double speed_limit = 5;        // 限速，可以从link中获取
  required uint64 timestamp_nsec = 6;     // 当前时间戳(纳秒）
  repeated Point3D navi_points_ego = 7;   // 2km导航路径, ego坐标系
  repeated Point3D navi_points_odom = 8;  // 2km导航路径, odom坐标系
}

enum NaviType{
  NOA = 1;
  LCC = 2;
};

enum LCCStatus{
  DEFAULT        = 1;   // NOA，占位
  MAP_FREE       = 2;   // MAP_FREE模式下默认LCC
  NO_ROUTE       = 3;   // 没有路线下发，或超过200m没有接到高德导航信息
  NO_ROUTE_POINT = 4;   // 有路线下发，但没有粗导航点
  REACH_NAVI_END = 5;   // 即将到达导航终点
  MPP_DEVIATION  = 6;   // MPP偏航
  OTHERS         = 7;
}

message NaviMode
{
  required uint64 timestamp_nsec = 1;
  required NaviType navi_type = 2;
}

message NaviRoute {
  required uint32 navi_route_type = 1;    // 1, gd; 2, bd
  optional NaviRouteGD navi_route_gd = 2;
  optional NaviRouteBD navi_route_bd = 3;
  optional NaviType navi_type = 4;

  optional uint32 navi_status = 5; // 粗导航状态位，目前仅判断0/1
  optional uint32 tbt_status = 6; // TBT信息状态位，目前仅判断0/1
  optional uint64 timestamp_nsec = 7; // 当前时间戳(纳秒)

  optional LCCStatus lcc_status = 8;
}
