#!/usr/bin/env python3
import os
import shutil

# 所有需要拷贝的文件列表（从依赖分析中获得，排除已存在的文件）
files_to_copy = [
    # dreamview_msgs
    "common_msgs/dreamview_msgs/chart.proto",
    
    # map_msgs
    "common_msgs/map_msgs/map.proto",
    "common_msgs/map_msgs/map_clear_area.proto", 
    "common_msgs/map_msgs/map_crosswalk.proto",
    "common_msgs/map_msgs/map_geometry.proto",
    "common_msgs/map_msgs/map_id.proto",
    "common_msgs/map_msgs/map_junction.proto",
    "common_msgs/map_msgs/map_lane.proto",
    "common_msgs/map_msgs/map_overlap.proto",
    "common_msgs/map_msgs/map_parking_space.proto",
    "common_msgs/map_msgs/map_pnc_junction.proto",
    "common_msgs/map_msgs/map_road.proto",
    "common_msgs/map_msgs/map_rsu.proto",
    "common_msgs/map_msgs/map_signal.proto",
    "common_msgs/map_msgs/map_speed_bump.proto",
    "common_msgs/map_msgs/map_stop_sign.proto",
    "common_msgs/map_msgs/map_yield_sign.proto",
    
    # perception_msgs
    "common_msgs/perception_msgs/MotionType.proto",
    "common_msgs/perception_msgs/ObjectFusion.proto",
    "common_msgs/perception_msgs/ObjectType.proto",
    "common_msgs/perception_msgs/Size3D.proto",
    "common_msgs/perception_msgs/TrafficlightTypes.proto",
    "common_msgs/perception_msgs/Vec2D.proto",
    "common_msgs/perception_msgs/Vec3D.proto",
    "common_msgs/perception_msgs/perception_obstacle.proto",
    "common_msgs/perception_msgs/traffic_light_detection.proto",
    
    # planning_msgs
    "common_msgs/planning_msgs/decision.proto",
    "common_msgs/planning_msgs/navigation.proto",
    "common_msgs/planning_msgs/planning.proto",
    "common_msgs/planning_msgs/planning_internal.proto",
    "common_msgs/planning_msgs/planning_status.proto",
    "common_msgs/planning_msgs/scenario_type.proto",
    "common_msgs/planning_msgs/sl_boundary.proto",
    
    # prediction_msgs
    "common_msgs/prediction_msgs/efficient_lane_change_feature.proto",
    "common_msgs/prediction_msgs/prediction_point.proto",
    
    # routing_msgs
    "common_msgs/routing_msgs/routing.proto",
]

def copy_files():
    for file_path in files_to_copy:
        src = file_path
        dst = f"test/modules/{file_path}"
        
        # 确保目标目录存在
        dst_dir = os.path.dirname(dst)
        os.makedirs(dst_dir, exist_ok=True)
        
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"Copied: {src} -> {dst}")
        else:
            print(f"Warning: Source file not found: {src}")

if __name__ == "__main__":
    copy_files()
    print("Copy completed!")
