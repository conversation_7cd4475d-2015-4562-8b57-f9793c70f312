#!/usr/bin/env python3
import os
import re
import sys

def find_proto_dependencies(proto_file, visited=None, base_dir="."):
    """递归查找proto文件的所有依赖"""
    if visited is None:
        visited = set()
    
    if proto_file in visited:
        return set()
    
    visited.add(proto_file)
    dependencies = set()
    dependencies.add(proto_file)
    
    full_path = os.path.join(base_dir, proto_file)
    if not os.path.exists(full_path):
        print(f"Warning: {full_path} not found")
        return dependencies
    
    try:
        with open(full_path, 'r') as f:
            content = f.read()
        
        # 查找import语句
        import_pattern = r'import\s+"([^"]+)"'
        imports = re.findall(import_pattern, content)
        
        for imp in imports:
            # 移除modules/前缀如果存在
            if imp.startswith("modules/"):
                imp = imp[8:]  # 移除"modules/"
            
            # 递归查找依赖
            sub_deps = find_proto_dependencies(imp, visited, base_dir)
            dependencies.update(sub_deps)
    
    except Exception as e:
        print(f"Error reading {full_path}: {e}")
    
    return dependencies

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python find_dependencies.py <proto_file>")
        sys.exit(1)
    
    proto_file = sys.argv[1]
    if proto_file.startswith("modules/"):
        proto_file = proto_file[8:]
    
    deps = find_proto_dependencies(proto_file)
    
    print("All dependencies:")
    for dep in sorted(deps):
        print(f"  {dep}")
