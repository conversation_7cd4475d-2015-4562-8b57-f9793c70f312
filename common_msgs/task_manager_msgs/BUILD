## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "task_manager_proto",
    srcs = ["task_manager.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/map_msgs:map_parking_space_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

apollo_package()
