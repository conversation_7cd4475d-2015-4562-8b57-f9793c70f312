## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "routing_proto",
    srcs = ["routing.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:error_code_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/map_msgs:map_geometry_proto",
        "//modules/common_msgs/map_msgs:map_parking_space_proto",
    ],
)

proto_library(
    name = "poi_proto",
    srcs = ["poi.proto"],
    deps = [
        ":routing_proto",
    ],
)

apollo_package()
