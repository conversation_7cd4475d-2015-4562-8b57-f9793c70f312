syntax = "proto2";

package robosense.prediction_debug;

message PostTbtInfoDebug {
    optional int32 passable = 1;
    optional int32 raw_turn_type = 2;
    repeated int32 pd_turn_type = 3;
}

message LaneSeqTurnTypeDebug {
    optional string lane_id = 1;
    repeated int32 turn_type = 2;
}

message PostGoalInfoDebug {
    optional double goal_x = 1;
    optional double goal_y = 2;
    optional double remain_distance = 3;
    optional int32 raw_goal_category = 4;
    repeated int32 pd_goal_category = 5;
}

message EfficientLaneChangeSequenceDebug {
    optional double cruise_probability = 1;
    optional double exit_probability = 2;
    optional double raw_model_probability = 3;
}

message EfficientLaneChangeDebug {
    repeated EfficientLaneChangeSequenceDebug efficient_lane_change_sequence_debug = 1;
    repeated PostTbtInfoDebug post_tbt_infos = 2;   
    repeated LaneSeqTurnTypeDebug debug_lane_seq_turn_type = 3;
    repeated PostGoalInfoDebug post_goal_infos = 4;
}


