## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "prediction_obstacle_debug_proto",
    srcs = ["prediction_obstacle_debug.proto"],
    deps = [
        ":efficient_lane_change_debug_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/prediction_msgs:feature_proto",
        "//modules/common_msgs/prediction_msgs:vehicle_flow_proto",
    ],
)

proto_library(
    name = "efficient_lane_change_debug_proto",
    srcs = ["efficient_lane_change_debug.proto"],
)

apollo_package()
