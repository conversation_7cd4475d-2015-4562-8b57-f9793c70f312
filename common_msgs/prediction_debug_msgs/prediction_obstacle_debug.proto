syntax = "proto2";

package robosense.prediction_debug;

import "modules/common_msgs/prediction_msgs/feature.proto";
import "modules/common_msgs/prediction_msgs/vehicle_flow.proto";
import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/prediction_debug_msgs/efficient_lane_change_debug.proto";

message AsyReasonerLogDebug {
  optional int64 timestamp = 1;
  repeated int64 increamental_timestamp = 2;
}

// estimated obstacle intent
message ObstacleIntentDebug {
  enum Type {
    UNKNOWN = 0;
    STOP = 1;
    STATIONARY = 2;
    MOVING = 3;
    CHANGE_LANE = 4;
    LOW_ACCELERATION = 5;
    HIGH_ACCELERATION = 6;
    LOW_DECELERATION = 7;
    HIGH_DECELERATION = 8;
  }
  optional Type type = 1 [default = UNKNOWN];
}

message PredictionObstacleDebug {
  optional int32 dbn_added_lane_intention = 1 [default = 0]; //debug

  optional int32 rule_added_lane_intention = 2 [default = 0]; //debug

  optional int32 acc_traffic_light_cnt = 3 [default = 0]; //debug

  optional int32 acc_static_front_obs_cnt = 4 [default = 0]; //debug

  optional int32 traj_kappa_check_cnt = 5 [default = 0]; //debug

  repeated int32 vehicle_flow_id = 6; // debug

  // estimated obstacle intent
  optional ObstacleIntentDebug intent = 7;  //debug

  optional apollo.prediction.ObstaclePriority priority = 8; //debug

  // Feature history latest -> earliest sequence
  repeated apollo.prediction.Feature feature = 9; //debug

  optional apollo.prediction.RsPnPPrediction rspnp_prediction = 10; //debug

  optional string intention_type = 11; //debug

  optional string obstacle_status = 12; // debug

  optional string goal_lane_in_junction = 13; //debug

  optional int32 obs_id = 14; //debug

  optional double timestamp = 15;
}

message PredictionObstaclesDebug {
  optional apollo.common.Header header = 1;

  optional AsyReasonerLogDebug asy_reasoner_log = 2;
  
  optional apollo.prediction.VehicleFlowLines vehicle_flow_lines = 3;

  repeated PredictionObstacleDebug prediction_obstacle_debug = 4;

  optional EfficientLaneChangeDebug efficient_lane_change_debug = 5;
}