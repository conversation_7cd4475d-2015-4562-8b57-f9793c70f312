
syntax = "proto2";

package apollo.drivers.gnss;

// pre-defined GNSS band frequency ID
enum GnssBandID {
  BAND_UNKNOWN = 0;
  GPS_L1 = 1;
  GPS_L2 = 2;
  GPS_L5 = 3;
  BDS_B1 = 4;
  BDS_B2 = 5;
  BDS_B3 = 6;
  GLO_G1 = 7;
  GLO_G2 = 8;
  GLO_G3 = 9;
}

// observation and ephemeris related system time type
enum GnssTimeType {
  TIME_UNKNOWN = 0;
  GPS_TIME = 1;
  BDS_TIME = 2;
  GLO_TIME = 3;
  GAL_TIME = 4;
}

// observation and ephemeris related system type
enum GnssType {
  SYS_UNKNOWN = 0;
  GPS_SYS = 1;
  BDS_SYS = 2;
  GLO_SYS = 3;
  GAL_SYS = 4;
}

// type of pseudo-range
enum PseudoType {
  CODE_UNKNOWN = 0;
  CORSE_CODE = 1;
  PRECISION_CODE = 2;
}

// This message defines one band observation of a certain satellite
message BandObservation {
  // observation on a certain frequency band
  optional GnssBandID band_id = 1 [default = BAND_UNKNOWN];
  optional double frequency_value = 2;
  optional PseudoType pseudo_type = 3 [default = CODE_UNKNOWN];
  // unit in meter
  optional double pseudo_range = 4;
  // unit in cycle
  optional double carrier_phase = 5;
  // Indicator of losing tracking of the signal
  optional uint32 loss_lock_index = 6;
  // unit in /s
  optional double doppler = 7;
  // Signal strength: signal noise ratio or carrier noise ratio
  optional float snr = 8;
}

// This message defines one satellite observation of a certain epoch
message SatelliteObservation {
  optional uint32 sat_prn = 1;
  optional GnssType sat_sys = 2 [default = GPS_SYS];
  optional uint32 band_obs_num = 3;
  repeated BandObservation band_obs = 4;
}

// This message defines one epoch observation on a certain time of a receiver
message EpochObservation {
  // Unique id to a certain receiver
  // 0 for rover, otherwise for baser, supporting multi-baser mode
  optional uint32 receiver_id = 1;
  optional GnssTimeType gnss_time_type = 2 [default = GPS_TIME];
  optional uint32 gnss_week = 3;
  optional double gnss_second_s = 4;
  // Baser observation should be bound with coordinates
  // unit in meter
  optional double position_x = 5;
  // unit in meter
  optional double position_y = 6;
  // unit in meter
  optional double position_z = 7;
  // Health indicator: 0 for healthy while 1 for bad observation
  optional uint32 health_flag = 8 [default = 0];
  // Number of observed satellites
  optional uint32 sat_obs_num = 9;
  // Group of observed satellite observation
  repeated SatelliteObservation sat_obs = 10;
}

// This message defines main six keppler orbit parameters and perturbations,
// designed for gps, beidou, (also supporting gnss, galileo)
message KepplerOrbit {
  optional GnssType gnss_type = 1 [default = GPS_SYS];
  optional uint32 sat_prn = 2;
  optional GnssTimeType gnss_time_type = 3 [default = GPS_TIME];
  // TOC: time of clock
  optional uint32 year = 4;
  optional uint32 month = 5;
  optional uint32 day = 6;
  optional uint32 hour = 7;
  optional uint32 minute = 8;
  optional double second_s = 9;
  // GNSS week number
  optional uint32 week_num = 10;
  optional double reserved = 11;
  optional double af0 = 12;   // clock correction(sec)
  optional double af1 = 13;   // clock correction(sec/sec)
  optional double af2 = 14;   // clock correction(sec/sec2)
  optional double iode = 15;  // Issue Of Data, Ephemeris in subframes 2 and 3
  optional double deltan =
      16;  // mean anomaly correction semi-circles per sec*pi = rads
  optional double m0 = 17;  // mean anomaly at ref time semi-circles*pi = rads
  optional double e = 18;   // eccentricity
  optional double roota = 19;  // sqr root a ( meters 1/2 )
  optional double toe = 20;    // ref time (sec) of ephemeris
  optional double toc = 21;    // ref time (sec) of clock
  optional double cic = 22;    // harmonic correction term(rads)
  optional double crc = 23;    // harmonic correction term(meters)
  optional double cis = 24;    // harmonic correction term(rads)
  optional double crs = 25;    // harmonic correction term(meters)
  optional double cuc = 26;    // harmonic correction term(rads)
  optional double cus = 27;    // harmonic correction term(rads)
  optional double omega0 =
      28;  // longitude  of ascending node semi-circles*pi = rads
  optional double omega = 29;  // argument of perigee semi-circles*pi
  optional double i0 = 30;     // inclination angle at ref time semi-circles*pi
  optional double omegadot = 31;  // rate of right ascension semi-circles/sec*pi
  optional double idot = 32;      // rate of inclination semi-circles/sec*pi
  optional double codesonL2channel = 33;  // pseudo range codes on L2
  optional uint32 L2Pdataflag = 34;       // data flag of L2P
  optional uint32 accuracy = 35;          // user range accuracy
  optional uint32 health = 36;            // satellite health: 0=good,1=bad
  optional double tgd = 37;               // group delay (s)
  optional double iodc = 38;              // Issue Of Data, Clock
}

// This message defines orbit parameters of GLONASS
message GlonassOrbit {
  optional GnssType gnss_type = 1 [default = GLO_SYS];
  optional uint32 slot_prn = 2;
  optional GnssTimeType gnss_time_type = 3 [default = GLO_TIME];
  // refer to GLONASS time and toc == toe
  optional double toe = 4;
  // must convert toe to UTC(+0) format and fulfill year, month...second_s,etc.
  optional uint32 year = 5;
  optional uint32 month = 6;
  optional uint32 day = 7;
  optional uint32 hour = 8;
  optional uint32 minute = 9;
  optional double second_s = 10;
  // unit in meter
  optional int32 frequency_no = 11;
  // GNSS week number
  optional uint32 week_num = 12;
  // GNSS week second in seconds
  optional double week_second_s = 13;
  // frame broadcasted time
  optional double tk = 14;
  // clock correction(sec/sec),warning: set clock_offset = -TauN
  optional double clock_offset = 15;
  // clock correction(sec/sec2),warning: set clock_drift = +GammaN
  optional double clock_drift = 16;
  // Satellite health : 0=good,1=bad
  optional uint32 health = 17;
  // unit in meter
  optional double position_x = 18;
  optional double position_y = 19;
  optional double position_z = 20;
  // unit in m/s
  optional double velocity_x = 21;
  optional double velocity_y = 22;
  optional double velocity_z = 23;
  // unit in m/s2
  optional double accelerate_x = 24;
  optional double accelerate_y = 25;
  optional double accelerate_z = 26;
  optional double infor_age = 27;
  optional uint32 sat_prn = 28;
}

// This message encapsulates keppler orbit message and glonass message
message GnssEphemeris {
  optional GnssType gnss_type = 1 [default = GLO_SYS];
  optional KepplerOrbit keppler_orbit = 2;
  optional GlonassOrbit glonass_orbit = 3;
}
