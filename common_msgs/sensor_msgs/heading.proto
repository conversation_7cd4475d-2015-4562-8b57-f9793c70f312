syntax = "proto2";

package apollo.drivers.gnss;

import "modules/common_msgs/basic_msgs/header.proto";

message Heading {
  optional apollo.common.Header header = 1;
  optional double measurement_time = 2;

  optional uint32 solution_status = 3;
  optional uint32 position_type = 4;

  optional float baseline_length = 5;
  optional float heading = 6;
  optional float pitch = 7;

  optional float reserved = 8;
  optional float heading_std_dev = 9;
  optional float pitch_std_dev = 10;

  optional bytes station_id = 11;

  optional uint32 satellite_tracked_number = 12;
  optional uint32 satellite_soulution_number = 13;
  optional uint32 satellite_number_obs =
      14;  // Number of satellites above the elevation mask angle
  optional uint32 satellite_number_multi =
      15;  // Number of satellites above the mask angle with L2

  optional uint32 solution_source = 16;
  optional uint32 extended_solution_status = 17;
  optional uint32 galileo_beidou_sig_mask = 18;
  optional uint32 gps_glonass_sig_mask = 19;
}
