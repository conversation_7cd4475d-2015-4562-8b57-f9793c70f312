syntax = "proto2";

package apollo.drivers.gnss;

import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/basic_msgs/header.proto";

// Measurements from an inertial measurement unit (IMU). The solution is with
// respect to the IMU by default.
message Imu {
  optional apollo.common.Header header = 1;

  // The time of IMU measurement, seconds since the GPS epoch (Jan 6, 1980).
  optional double measurement_time = 2;  // In seconds.

  // When measurement_span is non-zero, the gyroscope and accelerometer
  // measurements are averaged for the period from
  // (measurement_time - measurement_span) to measurement_time. Usually,
  //      measurement_span = 1 / sampling_frequency.
  //
  // When measurement_span is 0, angular_velocity and linear_acceleration are
  // instantaneous at measurement_time.
  optional float measurement_span = 3 [default = 0.0];  // In seconds.

  // Forward/left/up in meters per square second.
  optional apollo.common.Point3D linear_acceleration = 4;

  // Around forward/left/up axes in radians per second.
  optional apollo.common.Point3D angular_velocity = 5;
}
