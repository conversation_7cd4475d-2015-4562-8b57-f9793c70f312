syntax = "proto2";

package apollo.drivers;

import "modules/common_msgs/basic_msgs/header.proto";

message Ultrasonic {
  //
  //               x axis
  //		       ^
  //                     |
  //                  *  |  *
  //               *     |     *
  //        \    *       |       *    /
  //          \ *     range(i)    * /
  //            \        |        /
  //              \      |      /
  //                \    |    /
  //       y axis     \  |  /
  //      <---------------
  //        ooooooooooooo   //ultrasonic radar front surface
  //
  // In every working cycle, each radar of the ultrasonic system
  // return a range to form a range array, 'ranges'.

  optional apollo.common.Header header = 1;
  repeated float ranges = 2;
}
