syntax = "proto2";

package apollo.common.monitor;

import "modules/common_msgs/basic_msgs/header.proto";

message MonitorMessageItem {
  enum MessageSource {
    UNKNOWN = 1;
    CANBUS = 2;
    CONTROL = 3;
    DECISION = 4;
    LOCALIZATION = 5;
    PLANNING = 6;
    PREDICTION = 7;
    SIMULATOR = 8;
    HWSYS = 9;
    ROUTING = 10;
    MONITOR = 11;
    HMI = 12;
    RELATIVE_MAP = 13;
    GNSS = 14;
    CONTI_RADAR = 15;
    RACOBIT_RADAR = 16;
    ULTRASONIC_RADAR = 17;
    MOBILEYE = 18;
    DELPHI_ESR = 19;
    STORYTELLING = 20;
    TASK_MANAGER = 21;
  }

  optional MessageSource source = 1 [default = UNKNOWN];

  optional string msg = 2;

  enum LogLevel {
    INFO = 0;
    WARN = 1;
    ERROR = 2;
    FATAL = 3;
  }
  optional LogLevel log_level = 3 [default = INFO];
}

message MonitorMessage {
  optional apollo.common.Header header = 1;

  repeated MonitorMessageItem item = 2;
}
