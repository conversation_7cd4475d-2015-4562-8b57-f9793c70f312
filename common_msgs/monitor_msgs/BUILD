## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "smart_recorder_status_proto",
    srcs = ["smart_recorder_status.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "monitor_log_proto",
    srcs = ["monitor_log.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "system_status_proto",
    srcs = ["system_status.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

apollo_package()
