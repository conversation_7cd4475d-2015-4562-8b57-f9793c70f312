syntax = "proto2";

package apollo.monitor;

import "modules/common_msgs/basic_msgs/header.proto";

// Status summary. The value order is important, because when we summarize,
// larger value will overwrite smaller value:
//    FATAL > ERROR > WARN > OK > UNKNOWN
message ComponentStatus {
  enum Status {
    UNKNOWN = 0;
    OK = 1;
    WARN = 2;
    ERROR = 3;
    FATAL = 4;
  }
  optional Status status = 1 [default = UNKNOWN];
  optional string message = 2;
  optional double measure = 3[default = 0]; 
}

message Component {
  // A summary of all detailed status.
  optional ComponentStatus summary = 1;

  // Detailed status.
  optional ComponentStatus process_status = 2;
  optional ComponentStatus channel_status = 3;
  optional ComponentStatus resource_status = 4;
  optional ComponentStatus other_status = 5;
  optional ComponentStatus module_status = 6;

  // More Detailed status 
  map<string, ComponentStatus> resource_status_detail = 7; 
  map<string, ComponentStatus> channel_status_detail = 8; 
}

message SystemStatus {
  optional apollo.common.Header header = 1;

  map<string, ComponentStatus> hmi_modules = 7;
  map<string, Component> components = 8;

  // Some critical message for passengers. HMI should highlight it or even read
  // loudly.
  optional string passenger_msg = 4;

  // If we have this field, safety_mode should be triggered.
  // We'll check the system action and driver action continuously. If no proper
  // action was taken in a specified period of time (such as 10 seconds), EStop
  // will be sent to bring the vehicle into emergency full stop.
  optional double safety_mode_trigger_time = 5;
  optional bool require_emergency_stop = 6;

  // In simulation mode, the monitor will publish message with this field set,
  // so subscribers could identify it from the recorded messages.
  optional bool is_realtime_in_simulation = 9;

  // In some modes, other processes besides modules and monitored components may
  // need to be monitored
  map<string, ComponentStatus> other_components = 10;

  reserved 2, 3;
}
