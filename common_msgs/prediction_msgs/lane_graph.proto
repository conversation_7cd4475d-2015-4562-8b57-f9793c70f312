syntax = "proto2";

package apollo.prediction;

message LaneSegment {
  optional string lane_id = 1;
  optional uint32 lane_turn_type = 4 [default = 0];
  optional double total_length = 6 [default = 0.0];
}

message NearbyObstacle {
  optional int32 id = 1;
  optional double s = 2;  // relative to focus obstacle
  optional double l = 3;  // relative to focus obstacle
}

// next id = 25
message LaneSequence {
  optional int32 lane_sequence_id = 1;
  repeated LaneSegment lane_segment = 2;
  optional double probability = 6 [default = 0.0];

  enum LaneSequenceType{
    LEFT = 1;
    RIGHT = 2;
    STRAIGHT = 3;
    FORWARD = 4;   // lane seq froward obstacle position
    BACKWARD = 5;  // lane seq backward obstacle position
    UNKNOWN = 6;
  }

  optional LaneSequenceType lane_sequence_type = 24;
}

message LaneObstacle {
  optional int32 obstacle_id = 1;
  optional string lane_id = 2;
  optional double lane_s = 3;
  optional double lane_l = 4;
}
