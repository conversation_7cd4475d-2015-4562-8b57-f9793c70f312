syntax = "proto2";

package apollo.prediction;

import "modules/common_msgs/basic_msgs/error_code.proto";
import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/prediction_msgs/feature.proto";
import "modules/common_msgs/prediction_msgs/efficient_lane_change_feature.proto";
import "modules/common_msgs/perception_msgs/ObjectFusion.proto";
import "modules/common_msgs/prediction_msgs/vehicle_flow.proto";

// estimated obstacle intent
message ObstacleIntent {
  enum Type {
    UNKNOWN = 0;
    STOP = 1;
    STATIONARY = 2;
    MOVING = 3;
    CHANGE_LANE = 4;
    LOW_ACCELERATION = 5;
    HIGH_ACCELERATION = 6;
    LOW_DECELERATION = 7;
    HIGH_DECELERATION = 8;
  }
  optional Type type = 1 [default = UNKNOWN];
}

// self driving car intent
message Intent {
  enum Type {
    UNKNOWN = 0;
    STOP = 1;
    CRUISE = 2;
    CHANGE_LANE = 3;
  }
  optional Type type = 1 [default = UNKNOWN];
}

message InteractLon {
  optional double IGNORE_LON = 1;  // probablity: 0.0~1.0, same for below
  optional double FOLLOW = 2;
  optional double YIELD = 3;
  optional double OVERTAKE = 4;
}

message InteractLat {
  optional double IGNORE_LAT = 1;
  optional double BYPASS_LEFT = 2;
  optional double BYPASS_RIGHT = 3;
}

message Decision {
  optional double probability = 1;
  required InteractLon interact_lon = 2;
  required InteractLat interact_lat = 3;
}

message AsyReasonerLog {
  optional int64 timestamp = 1;
  repeated int64 increamental_timestamp = 2;
}

message PredictionObstacle {
  optional robosense.perception_msgs.ObjectFusion post_fusion_perception_obstacle = 15;

  optional double timestamp = 2;  // GPS time in seconds
  // the length of the time for this prediction (e.g. 10s)
  optional double predicted_period = 3;
  // can have multiple trajectories per obstacle
  repeated Trajectory trajectory = 4;

  // estimated obstacle intent
  optional ObstacleIntent intent = 5 [deprecated = true];

  optional ObstaclePriority priority = 6;

  optional ObstacleInteractiveTag interactive_tag = 9 [deprecated = true];

  optional string obstacle_status = 10 [deprecated = true];

  optional string goal_lane_in_junction = 12 [deprecated = true];

  optional bool is_static = 7 [default = false];

  // Feature history latest -> earliest sequence
  repeated Feature feature = 8;

  optional string intention_type = 11 [deprecated = true];

  repeated Decision decision = 13;

  optional RsPnPPrediction rspnp_prediction = 14 [deprecated = true];

  repeated PathIntention path_intention = 16;

  optional int32 dbn_added_lane_intention = 17 [deprecated = true];

  optional int32 rule_added_lane_intention = 18 [deprecated = true];

  optional int32 acc_traffic_light_cnt = 19 [deprecated = true];

  optional int32 acc_static_front_obs_cnt = 20 [deprecated = true];

  optional int32 traj_kappa_check_cnt = 21 [deprecated = true];

  repeated int32 vehicle_flow_id = 22 [deprecated = true];
}

message PredictionObstacles {
  // timestamp is included in header
  optional apollo.common.Header header = 1;

  // make prediction for multiple obstacles
  repeated PredictionObstacle prediction_obstacle = 2;

  // perception error code
  optional apollo.common.ErrorCode perception_error_code = 3 [deprecated = true];

  // perception timestamp
  optional int64 perception_timestamp = 8 [default = -1];

  // localization timestamp
  optional int64 localization_timestamp = 9 [default = -1];

  // planning timestamp
  optional int64 planning_timestamp = 10 [default = -1];

  // story timestamp
  optional int64 story_timestamp = 11 [default = -1];

  // routing timestamp
  optional int64 routing_timestamp = 12 [default = -1];

  // traffic_light timestamp
  optional int64 traffic_light_timestamp = 13 [default = -1];

  optional AsyReasonerLog asy_reasoner_log = 14 [deprecated = true];

  optional EfficientLaneChange efficient_lane_change = 15;

  // road_structure timestamp
  optional int64 road_structure_timestamp = 16 [default = -1];

  optional VehicleFlowLines vehicle_flow_lines = 17 [deprecated = true];

  // navi route timestamp
  optional int64 navi_route_timestamp = 18 [default = -1];

  optional EgoPathProposalSet ego_path_proposal_set = 19;

  //map mode
  optional MapMode map_mode = 20;
}
