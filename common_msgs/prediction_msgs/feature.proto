syntax = "proto2";

package apollo.prediction;

import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/basic_msgs/pnc_point.proto";
import "modules/common_msgs/map_msgs/map_lane.proto";
import "modules/common_msgs/perception_msgs/perception_obstacle.proto";
import "modules/common_msgs/prediction_msgs/lane_graph.proto";
import "modules/common_msgs/prediction_msgs/prediction_point.proto";
import "modules/common_msgs/prediction_msgs/efficient_lane_change_feature.proto";
import "modules/common_msgs/prediction_msgs/vehicle_flow.proto";
import "modules/common_msgs/perception_msgs/ObjectFusion.proto";
import "modules/common_msgs/prediction_debug_msgs/efficient_lane_change_debug.proto";

message Lane {
  // Features of all possible current lanes.
  repeated LaneFeature current_lane_feature = 1;

  // Features of the most possible current lane.
  optional LaneFeature lane_feature = 2;

  // Features of all nearby lanes.
  repeated LaneFeature nearby_lane_feature = 3;

  // Sorted lane idx
  repeated int32 left_lane_idx = 6;
  repeated int32 right_lane_idx = 7;
  repeated int32 horizontal_cut_in_idx = 8;
  repeated int32 horizontal_cut_out_idx = 9;

  // For ADC
  repeated LaneFeature range_lane_feature = 27;
}

message LaneFeature {
  optional string lane_id = 1;
  optional uint32 lane_turn_type = 2;
  optional double lane_s = 3;
  optional double lane_l = 4;
  optional double angle_diff = 5;
  optional double dist_to_left_boundary = 6;
  optional double dist_to_right_boundary = 7;
  optional double lane_heading = 8;
  optional apollo.hdmap.Lane.LaneType lane_type = 9;

  enum Relation {
    FORWARD = 1;
    REVERSE_FORWARD = 2;
    HORIZONTAL_CUT_IN = 3;
    HORIZONTAL_CUT_OUT = 4;
  }

  optional Relation relation_to_obstacle = 10;
}

message JunctionExit {
  optional string exit_lane_id = 1;
  optional apollo.common.Point3D exit_position = 2;
  optional double exit_heading = 3;
  optional double exit_width = 4;
  optional double probability = 5;
}

message JunctionFeature {
  optional string junction_id = 1;
  optional double junction_range = 2;
  optional LaneFeature enter_lane = 3;
  repeated JunctionExit junction_exit = 4;
  repeated string start_lane_id = 8;
  repeated JunctionExit exits_expected_by_start = 9;
  optional double enter_theta = 10;
}

message ObstaclePriority {
  enum Priority {
    CAUTION = 1;
    NORMAL = 2;
    IGNORE = 3;
  }
  optional Priority priority = 25 [default = NORMAL];
}

message ObstacleInteractiveTag {
  enum InteractiveTag {
    INTERACTION = 1;
    NONINTERACTION = 2;
  }
  optional InteractiveTag interactive_tag = 37 [default = NONINTERACTION];
}

message PointUncertainty {
  optional double s_sigma = 1;
  optional double l_sigma = 2;
  optional double theta = 3;
}

message RsPnPUncertainty {
  optional double sigma_x = 1;
  optional double sigma_y = 2;
  optional double rho = 3;
}


message Trajectory {
  optional double probability = 1;  // probability of this trajectory
  repeated apollo.common.TrajectoryPoint trajectory_point = 2;
  repeated string selected_exit_lane_id = 3;
  repeated string selected_group_lane_ids = 4;
  repeated LaneSequence lane_sequence = 5;
  optional apollo.hdmap.Lane.LaneTurn turn_type = 6;
  repeated PointUncertainty traj_uncertainty = 7;
  enum Type {
    UNKNOWN = 0;
    INTENTION = 1;
    MOTION = 2;
  };
  optional Type type = 8;
  repeated string current_lane_id = 9;
  repeated RsPnPUncertainty rspnp_traj_uncertainty = 10;
}

message RsPnPOutput {
  optional bytes future_prob = 1;
  optional bytes traj = 2;
  optional bytes lane_preference = 3;

  optional int32 future_len = 4;

  optional bytes goal_intention = 5;
  optional bytes lane_intention = 6;

  optional bytes decision_latitude_pred = 7;
  optional bytes decision_longitude_pred = 8;

  optional bytes decision_occ_latitude_pred = 9;
  optional bytes decision_occ_longitude_pred = 10;

  optional bytes active_lane_prefer = 11;
  optional bytes ego_exit_lane_seqs_prefer = 12;

  optional bytes ego_path = 13;
  optional bytes vt_profile = 14;

  optional bytes decision_latitude_pred_corr_path = 15;
  optional bytes decision_longitude_pred_corr_path = 16;
  optional bytes decision_occ_latitude_pred_corr_path = 17;
  optional bytes decision_occ_longitude_pred_corr_path = 18;

  optional bytes decision_curb_latitude_pred = 19;
  optional bytes decision_curb_longitude_pred = 20;
  optional bytes decision_curb_latitude_pred_corr_path = 21;
  optional bytes decision_curb_longitude_pred_corr_path = 22;

  optional bytes traj_uncertainty = 23;

  optional bytes path_output_location_classes = 24;
  optional bytes path_output_location_coords = 25;
  optional bytes path_output_location_vts = 26;
  optional bytes path_output_location_spatial_coord = 27;
}

message RsPnPFeature {
  optional RsPnPOutput model_output = 1;

  // common info
  repeated int32 obstacle_id_map = 2;
  optional int32 number_of_obstacle = 3;
  optional bytes lane_idx = 4;
  repeated string lane_id_map = 5;
  repeated RsPnPLaneSequenceIndices all_lane_seqs = 6;
  optional int32 number_of_lane_seq = 7;
  optional double curr_ego_x = 8;
  optional double curr_ego_y = 9;
  optional double curr_ego_theta = 10;
  optional double curr_ego_velocity = 11;
  repeated int32 occ_id_map = 12;

  // input features
  optional bytes obstacle_state = 13;
  optional bytes obstacle_state_mask = 14;
  optional bytes lane_seq_state = 15;
  optional bytes lane_seq_state_mask = 16;
  optional bytes lane_seq_state_bus = 17;
  optional bytes junction_obs_feat = 18;
  optional bytes junction_goal_feat = 19;
  optional bytes junction_lane_feat = 20;
  optional bytes decision_ego_future_path = 21;
  optional bytes start_lane_seqs_ind = 22;
  optional bytes end_lane_seqs_ind = 23;
  optional bytes coarse_path = 24;
  optional bytes raw_coarse_path = 25;
  optional bytes occ_state = 26;
  optional bytes curb_state = 27;
  optional bytes map_state_turn_type_state = 28;
  optional bytes tbt_lane_state = 29;
  optional bytes tbt_goal_state = 30;
  optional bytes lane_lights_info_matrix = 31;
  optional bytes map_state_crosswalk_state = 32;
  optional bytes obstacle_state_vt_guideline = 33;
  optional bytes road_net_ret_feature_map = 34;
  optional bytes road_net_ret_alignment_param = 35;
  
  // PD2.0
  optional bytes road_net_ret_high_feature_map = 36;
  optional bytes road_net_ret_low_feature_map = 37;
  optional bytes road_net_ret_carflow = 38;
  optional bytes backward_path = 39;
  optional bytes backward_5s = 40;
  optional bytes history_mask = 41;
  optional bytes traffic_matrix = 42;
  optional bytes path_road_net_ret_carflow = 43;
  optional bytes vt_guideline = 44;
  optional bytes road_marker_array_state = 45;
}

message RsPnPLaneSequenceIndices {
  repeated int32 lane_id_indices = 1;
}

message RsPnPLaneSequence {
  repeated string lane_ids = 1;
  optional double probability = 2;
}

message RsPnPDecisionLatitude {
  enum LatitudePredType {
    IGNORE_LAT = 1;
    BYPASS_LEFT = 2;
    BYPASS_RIGHT = 3;
  }
  optional LatitudePredType latitude_pred_type = 1 [default = IGNORE_LAT];
  optional double prob = 2;
}

message RsPnPDecisionLongitude {
  enum LongitudePredType {
    IGNORE_LON = 1;
    FOLLOW = 2;
    YIELD = 3;
    OVERTAKE = 4;
  }
  optional LongitudePredType longitude_pred_type = 1 [default = IGNORE_LON];
  optional double prob = 2;
}

message RsPnPLaneIntention {
  optional double probability = 1;
  optional string lane_id = 2;
}

message RsPnPGoalIntention {
  optional double probability = 1;
  repeated RsPnPLaneIntention exit_lanes = 2;
}

message RsPnPPrediction {
  repeated Trajectory prediction_trajectory = 1;
  repeated RsPnPLaneSequence lane_sequence = 2;
  repeated RsPnPDecisionLatitude decision_latitude = 3;
  repeated RsPnPDecisionLongitude decision_longitude = 4;
  repeated RsPnPGoalIntention goal_lane_intentions = 5;
  optional EfficientLaneChange efficient_lane_change = 6;
  repeated RsPnPDecisionLatitude decision_latitude_opt = 7;
  repeated RsPnPDecisionLongitude decision_longitude_opt = 8;
  optional robosense.prediction_debug.EfficientLaneChangeDebug efficient_lane_change_debug = 9;
  optional EgoPathProposalSet ego_path_proposal_set = 10;
}

message ObstaclePriorityScores {
  optional double pose_cost = 1;
  optional double thw_cost = 2;
  optional double ttc_cost = 3;
  optional double intrusion_cost = 4;
  optional double total_cost = 5;
}

message FeatureAccManager {
  optional double acc_percep = 1;
  optional double acc_kalman = 2;
  optional double acc_filter = 3;
  optional double acc_rspnp = 4;
  optional double acc_cipv = 5;
  optional double acc_selected_with_feature = 6;
  optional double acc_rspnp_raw = 7;
  optional double acc_traffic_light = 8;
}

message LaneSeqAccManager {
  optional double acc_traffic_light = 1;
  optional double acc_static_front_obs = 2;
  optional double acc_selected_with_lane_seq = 3;
}

message ReasonerLaneResult {
  optional LaneSequence lane_sequence = 1;
  optional double acc_limited_by_scenario = 2;
  optional LaneSeqAccManager lane_seq_acc_manager = 3;
  enum SpecialSceneType {
    NONE = 0;
    RISK_LANE_KEEP = 1;
  }
  optional SpecialSceneType special_scene_type = 4 [default = NONE];
}

message ReasonerJunctionResult {
  optional JunctionExit junction_exit = 1;
}

message ReasonerResults {
  repeated ReasonerLaneResult lane_results = 1;
  repeated ReasonerJunctionResult junction_results = 2;
  optional bool risk_identify = 3;
  enum OppoObsTrafficLight {
    None = 0;
    Straight_Green_TurnLeft_Green = 1;
    Straight_Green_TurnLeft_Red = 2;
    Straight_Red_TurnLeft_Green = 3;
    Unknown = 4;
  }
  optional OppoObsTrafficLight oppo_obs_traffic_light = 4 [default = None];
}

message PathIntention {
  repeated apollo.common.PathPoint path_point = 1;
}

// next id = 59
message Feature {
  // Obstacle ID
  optional int32 id = 1;

  // Obstacle features
  repeated apollo.common.Point3D polygon_point = 30;
  optional apollo.common.Point3D position = 2;
  optional apollo.common.Point3D front_position = 27;
  optional apollo.common.Point3D velocity = 3;
  optional apollo.common.Point3D raw_velocity = 28;  // from perception
  optional apollo.common.Point3D acceleration = 4;
  optional double velocity_heading = 5;
  optional double speed = 6;
  optional double acc = 7;
  optional double yaw_rate = 43;
  optional double theta = 8;
  optional double length = 9;
  optional double width = 10;
  optional double height = 11;
  optional double tracking_time = 12;
  optional double timestamp = 13;
  optional FeatureAccManager feature_acc_manager = 44;
  repeated int32 ego_cipv_ids = 45;

  // Obstacle type-specific features
  optional Lane lane = 14;
  optional JunctionFeature junction_feature = 26;

  // Obstacle tracked features
  optional apollo.common.Point3D t_position = 16;
  optional apollo.common.Point3D t_velocity = 17 [deprecated = true];
  optional double t_velocity_heading = 18 [deprecated = true];
  optional double t_speed = 19 [deprecated = true];
  optional apollo.common.Point3D t_acceleration = 20 [deprecated = true];
  optional double t_acc = 21 [deprecated = true];

  optional bool is_still = 22 [default = false];
  optional apollo.perception.PerceptionObstacle.Type type = 23;
  optional double label_update_time_delta = 24;

  optional ObstaclePriority priority = 25;

  optional bool is_near_junction = 29 [default = false];

  // Obstacle ground-truth labels:
  repeated PredictionTrajectoryPoint future_trajectory_points = 31;

  // Obstacle short-term predicted trajectory points
  repeated apollo.common.TrajectoryPoint
      short_term_predicted_trajectory_points = 32;

  // Obstacle predicted trajectories
  repeated Trajectory predicted_trajectory = 33;

  // ADC trajectory at the same frame, and ADC trajectory timestamp
  repeated apollo.common.TrajectoryPoint adc_trajectory_point = 34;
  optional double adc_timestamp = 38;
  optional apollo.perception.PerceptionObstacle adc_localization = 39;

  // Surrounding lanes
  repeated string surrounding_lane_id = 35;
  repeated string within_lane_id = 36;

  // RsPnP model prediction
  optional RsPnPPrediction rspnp_prediction = 40;
  optional ObstaclePriorityScores obstacle_priority_scores = 41;
  
  // Reasoning results
  optional ReasonerResults reasoner_results = 42;

  // Dynamic Bayesian State
  optional bool traced_dbn_state = 46 [default = false];
  repeated PathIntention path_intention = 47;

  // obstacle exist confidence, 0-high, 1-medium, 2-low
  optional uint32 exist_confidence = 48 [default = 0];

  //strategy statistic
  optional int32 dbn_added_lane_intention = 49 [default = 0];
  optional int32 rule_added_lane_intention = 50 [default = 0];
  optional int32 acc_traffic_light_cnt = 51 [default = 0];
  optional int32 acc_static_front_obs_cnt = 52 [default = 0];
  optional int32 traj_kappa_check_cnt = 53 [default = 0];

  // Vehicle flow lines of this obstacle
  optional VehicleFlowLines vehicle_flow_lines = 54;

  message LateralLaneProbability {
    optional double left = 1;
    optional double keep = 2;
    optional double right = 3;
  }

  optional LateralLaneProbability model_probs = 55;
  optional LateralLaneProbability dbn_probs = 56;

  message DbnObservation {
    optional double angle_diff = 1;
    optional double head_lane_l_ratio = 2;
    optional double corner_ttb = 3;
    optional double left_lane_theta = 4;
    optional double right_lane_theta = 5;
  }

  optional DbnObservation dbn_observation = 57;
  optional uint32 light_status = 58;
  optional uint32 yaw_rate_confidence = 59; // 0-No, 1-Low, 2-High

  //map mode
  optional MapMode map_mode = 60;
}

// next id = 13
message OccFeature {
  optional int32 id = 1;
  repeated apollo.common.Point3D polygon_point = 2;
  optional apollo.common.Point3D position = 3;
  optional double theta = 4;
  optional double length = 5;
  optional double width = 6;
  optional double height = 7;
  optional double tracking_time = 8;
  optional double timestamp = 9;

  // obstacle exist confidence, 0-high, 1-medium, 2-low
  optional uint32 exist_confidence = 10 [default = 0];

  enum OccType {
    OCCUPANCY = 1;
    ROAD_MARKER = 2;
    NOPE = 3;
  }

  optional OccType occ_type = 11 [default = NOPE];
  optional RsPnPPrediction rspnp_prediction = 12;
  optional bool occ_in_roi = 13 [default = true];
  optional robosense.perception_msgs.ObjectFusion post_fusion_obstacle = 14;
}

message ObstacleHistory {
  repeated Feature feature = 1;
  optional bool is_trainable = 2 [default = false];
}

message FrameEnv {
  optional double timestamp = 1;
  optional ObstacleHistory ego_history = 2;
  repeated ObstacleHistory obstacles_history = 3;
}

enum MapMode {
  LANE_MAP_MODE = 0;
  MAP_FREE_MODE = 1;
  LANE_MAP_ENHANCE_MODE = 2;
}