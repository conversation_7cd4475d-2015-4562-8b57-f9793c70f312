syntax = "proto2";

package apollo.prediction;

import "modules/common_msgs/prediction_msgs/prediction_point.proto";

enum ResultType {
  COARSE = 0;
  CRUISE = 1;
  PAIR = 2;
  IN_JUNCTION = 3;
}

enum ConnectionType {
    LEGAL = 0;
    ILLEGAL = 1;
}

message VTProfilePoint {
    optional double v = 1;
    optional double t = 2;
    optional double s = 3;
    optional double var = 4;
}

message InteractLonProb {
    optional double ignore_lon = 1;  // probablity: 0.0~1.0, same for below
    optional double follow = 2;
    optional double yield = 3;
    optional double overtake = 4;
}

message InteractLatProb {
    optional double ignore_lat = 1;
    optional double bypass_left = 2;
    optional double bypass_right = 3;
}

message DecisionRetPair {
    optional uint32 obs_id = 1;
    optional InteractLonProb interact_lon = 2;
    optional InteractLatProb interact_lat = 3;
}

message RawCurbTag {
    optional uint32 curb_idx = 1;
    optional double curb_start_x = 2;
    optional double curb_start_y = 3;
    optional double curb_end_x = 4;
    optional double curb_end_y = 5;
    optional InteractLonProb interact_lon = 6;
    optional InteractLatProb interact_lat = 7;
    optional bool valid_tag = 8 [default = false];
    optional double curb_start_height = 9;
    optional double curb_end_height = 10;
    optional uint32 curb_start_confidence = 11;
    optional uint32 curb_end_confidence = 12;
    optional bool curb_in_roi = 13 [default = true];
}

message RawCurbPolyline {
    optional uint32 polyline_idx = 1;
    repeated uint32 curb_idx_included = 2;
}

message DebugLaneSeqTurnType {
    optional string lane_id = 1;
    repeated int32 turn_type = 2;
}

enum ActionType {
    // 随开发进展 action 类别可能有所调整
    UNKNOWN = 0;
    GO_STRAIGHT = 1; // 直行
    LANE_CHANGE_LEFT = 2; // 向左变道
    LANE_CHANGE_RIGHT = 3; // 向右变道
    GO_STRAIGHT_IN_JUNCTION = 4; // 路口直行
    TURN_LEFT = 5; // 路口左转
    TURN_RIGHT = 6; // 路口右转
    U_TURN = 7; // 路口掉头
}

enum RoadActionType {
    ROAD_UNKNOWN = 0;
    ROAD_CRUISE = 1;
    ROAD_CHANGE_LEFT = 2;
    ROAD_CHANGE_RIGHT = 3;
    ROAD_CHANGE_MIDDLE = 4;
    ROAD_CHANGE_UTURN = 5;
    ROAD_IN_INTERSECTION = 6;
}

message EfficientLaneSequence {
    repeated string lane_ids = 1;
    optional double probability = 2;
    repeated PredictionPathPoint ego_path_point = 3;
    repeated PredictionPathPoint road_structure_point = 4;
    repeated VTProfilePoint vt_profile_point = 5;
    repeated DecisionRetPair decision_ret_pair = 6;
    optional ResultType type = 7;

    //debug
    optional double cruise_probability = 8 [deprecated = true];
    //debug
    optional double exit_probability = 9 [deprecated = true];

    repeated DecisionRetPair curb_decision_pair = 10;

    //debug
    optional double raw_model_probability = 11 [deprecated = true];

    repeated double alter_probabilities = 12;
    repeated string entry_lane_ids = 13;
    repeated string exit_lane_ids = 14;
    optional uint32 lane_seq_index = 15;
    optional ConnectionType connection_type = 16;
    repeated DecisionRetPair raw_decision_ret_pair = 17;
    optional bool advance_exit = 18 [default = false];
    repeated ActionType global_action = 19;
}

message EfficientLaneChange {
    repeated EfficientLaneSequence efficient_lane_sequence = 1;
    repeated RawCurbTag raw_curb_tags = 2;
    optional uint32 model_output_curb_tag_count = 3;
    repeated RawCurbPolyline curb_polylines = 4;
    //debug
    repeated PostTbtInfo post_tbt_infos = 5 [deprecated = true];
    //debug
    repeated DebugLaneSeqTurnType debug_lane_seq_turn_type = 6 [deprecated = true];
    //debug
    repeated PostGoalInfo post_goal_infos = 7 [deprecated = true];
    optional bool use_perception_lane = 8 [default = false];
}

message PostTbtInfo {
    optional int32 passable = 1;
    optional int32 raw_turn_type = 2;
    repeated int32 pd_turn_type = 3;
}

message PostGoalInfo {
    optional double goal_x = 1;
    optional double goal_y = 2;
    optional double remain_distance = 3;
    optional int32 raw_goal_category = 4;
    repeated int32 pd_goal_category = 5;
}



message EgoPathProposal {
    optional double score = 1;
    repeated ActionType global_action = 2;
    optional bool is_standard_action = 3 [default = false];  // 是否是网络预留的标准模态输出

    message ReferencePoint {
        optional double x = 1;
        optional double y = 2;
        optional double accumulated_s = 3;  // 用来定位这个参考点在精确path上的位置
        optional ActionType action = 4;
    }

    repeated ReferencePoint reference_line = 4;
    repeated PredictionPathPoint path = 5;
    repeated VTProfilePoint vt_profile_points = 6;
    repeated DecisionRetPair decision_ret_pair = 7;
    repeated DecisionRetPair curb_decision_pair = 8;
    optional int32 path_idx = 9 [default = -1];
}

message ActionPathProposal {
    optional double score = 1;
    optional int32 action_idx = 2;

    message ActionPoint {
        optional double x = 1;
        optional double y = 2;
        optional ActionType lane_type = 3;
        optional RoadActionType road_type = 4;
    }
    repeated ActionPoint action_line = 3;
}

message EgoPathProposalSet {
    repeated EgoPathProposal ego_path_proposals = 1;
    repeated ActionPathProposal action_path_proposals = 2;
}
