## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "feature_proto",
    srcs = ["feature.proto"],
    deps = [
        ":efficient_lane_change_feature_proto",
        ":lane_graph_proto",
        ":prediction_point_proto",
        ":vehicle_flow_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/map_msgs:map_lane_proto",
        "//modules/common_msgs/perception_msgs:ObjectFusion_proto",
        "//modules/common_msgs/perception_msgs:perception_obstacle_proto",
        "//modules/common_msgs/prediction_debug_msgs:efficient_lane_change_debug_proto",
    ],
)

proto_library(
    name = "prediction_point_proto",
    srcs = ["prediction_point.proto"],
)

proto_library(
    name = "vehicle_flow_proto",
    srcs = ["vehicle_flow.proto"],
)

proto_library(
    name = "lane_graph_proto",
    srcs = ["lane_graph.proto"],
)

proto_library(
    name = "efficient_lane_change_feature_proto",
    srcs = ["efficient_lane_change_feature.proto"],
    deps = [
        ":prediction_point_proto",
    ],
)

proto_library(
    name = "prediction_obstacle_proto",
    srcs = ["prediction_obstacle.proto"],
    deps = [
        ":efficient_lane_change_feature_proto",
        ":feature_proto",
        ":vehicle_flow_proto",
        "//modules/common_msgs/basic_msgs:error_code_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/perception_msgs:ObjectFusion_proto",
    ],
)

apollo_package()
