## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "decision_proto",
    srcs = ["decision.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:vehicle_signal_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

proto_library(
    name = "global_goal_proto",
    srcs = ["global_goal.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:geometry_proto",
    ],
)

proto_library(
    name = "sl_boundary_proto",
    srcs = ["sl_boundary.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
    ],
)

proto_library(
    name = "pad_msg_proto",
    srcs = ["pad_msg.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "planning_internal_proto",
    srcs = ["planning_internal.proto"],
    deps = [
        ":decision_proto",
        ":navigation_proto",
        ":scenario_type_proto",
        ":sl_boundary_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/chassis_msgs:chassis_proto",
        "//modules/common_msgs/dreamview_msgs:chart_proto",
        "//modules/common_msgs/localization_msgs:localization_proto",
        "//modules/common_msgs/map_msgs:map_id_proto",
        "//modules/common_msgs/perception_msgs:ObjectFusion_proto",
        "//modules/common_msgs/perception_msgs:traffic_light_detection_proto",
        "//modules/common_msgs/prediction_msgs:efficient_lane_change_feature_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

proto_library(
    name = "planning_proto",
    srcs = ["planning.proto"],
    deps = [
        ":decision_proto",
        ":planning_internal_proto",
        ":planning_status_proto",
        "//modules/common_msgs/basic_msgs:drive_state_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/chassis_msgs:chassis_proto",
        "//modules/common_msgs/map_msgs:map_id_proto",
        "//modules/common_msgs/prediction_msgs:efficient_lane_change_feature_proto",
    ],
)

proto_library(
    name = "navigation_proto",
    srcs = ["navigation.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/localization_msgs:localization_proto",
        "//modules/common_msgs/map_msgs:map_proto",
        "//modules/common_msgs/perception_msgs:perception_obstacle_proto",
    ],
)

proto_library(
    name = "planning_status_proto",
    srcs = ["planning_status.proto"],
    deps = [
        ":scenario_type_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:vehicle_signal_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

proto_library(
    name = "scenario_type_proto",
    srcs = ["scenario_type.proto"],
)

apollo_package()
