syntax = "proto2";

package apollo.planning;

import "modules/common_msgs/basic_msgs/geometry.proto";

message Vertex {
    optional double x = 1;
    optional double y = 2;
}

enum BehaviorType {
    STRUCTURED_PREFERENCE  = 0;
    UNSTRUCTURED_PREFERENCE  = 1;
};

message BehaviorPreference {
    required int32 tracker_id = 1;  // tracker_id of ego or agent
    optional int32 cur_lane_idx = 2;
    optional int32 goal_lane_idx = 3;
    optional double x = 4;
    optional double y = 5;
    required apollo.common.Polygon polygon = 6;
    required BehaviorType behavior_type = 7;
    optional double confidence = 8;
}

message GlobalGoal {
    required int64 global_goal_id = 1 [default = -1];
    repeated BehaviorPreference preferences = 2;
}

enum InteractTypeLat {
    IGNORE_LAT = 0;
    BYPASS = 1;
}

enum InteractTypeLon {
    IGNORE_LON = 0;
    FOLLOW = 1;
    YIELD = 2;
    OVERTAKE = 3;
}

message InteractObj {
    required int32 tracker_id = 1;
    required InteractTypeLat interact_type_lat = 2;
    required InteractTypeLon interact_type_lon = 3;
    required bool valid_flag = 4 [default = false];
}

message Decision {
    required int32 tracker_id = 1;
    repeated InteractObj interact_obj = 2;
}