syntax = "proto2";

package apollo.core_planning;

import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/routing_msgs/routing.proto";
import "modules/common_msgs/planning_msgs/scenario_type.proto";
import "modules/common_msgs/basic_msgs/vehicle_signal.proto";

/*
  This file defines the data types that represents the internal state of the
  planning module.
  It will not be refreshed in each planning cycle.
*/

message BareIntersectionStatus {
  optional string current_pnc_junction_overlap_id = 1;
  optional string done_pnc_junction_overlap_id = 2;
  optional uint32 clear_counter = 3;
}

message TargetGap {
  optional string target_front_obs = 1;

  optional string target_rear_obs = 2;

  optional double start_timestamp = 3;

  optional bool is_near = 4 [default = false];

  optional double near_start_timestamp = 5;
}

message RefLineId {
  repeated string lane_id = 1;
}

message ChangeLaneStatus {
  enum Status {
    LANE_KEEP = 0;
    LANE_CHANGE_AWAIT = 1;
    IN_LANE_CHANGE = 2;
    REROUTE_FALLBACK = 3;
  }
  enum LaneChangeDirection {
    NO_CHANGE = 0;
    LEFT_CHANGE = 1;
    RIGHT_CHANGE = 2;
  }
  enum LaneChangeType {
    NONE = 0;
    PADDLE_SHIFTED = 1;
    NAVIGATION = 2;
    EFFICIENCY = 3; 
  }
  optional Status status = 1 [default = REROUTE_FALLBACK];

  // origin reference line for lane change
  optional RefLineId origin_reference_line_id = 2;

  // target reference line for lane change
  optional RefLineId target_reference_line_id = 3;

  // planned reference line for lane change
  optional RefLineId planned_reference_line_id = 4;

  // the starting position only after which lane-change can happen.
  optional bool exist_lane_change_start_position = 5 [default = false];
  optional apollo.common.Point3D lane_change_start_position = 6;

  // denotes if the surrounding area is clear for ego vehicle to
  // change lane at this moment.
  optional bool is_clear_to_change_lane = 7 [default = false];

  optional double lat_ref_origin = 8 [default = 0.0];
  optional double lat_ref_target = 9 [default = 0.0];
  optional double lat_ref_await_target = 10 [default = 0.0];

  optional bool never_change_lane_finished = 20 [default = true];

  optional bool is_lane_change_never_lat_free = 21 [default = true];

  optional double lane_change_lat_free_start_timestamp = 22;

  optional double in_lane_change_start_timestamp = 23;

  // the last time stamp when the lane-change planning succeed.
  optional double last_finished_timestamp = 24;

  // the last time stampe when fsm enter await status
  optional double last_waited_timestamp = 25;

  // the last time stamp when the lane-change planning fails.
  optional double last_fallback_timestamp = 26;

  optional LaneChangeType lane_change_type = 27 [default = NONE];

  optional LaneChangeDirection lane_change_direction = 28 [default = NO_CHANGE];

  optional bool is_reached_origin_path = 29 [default = true];

  optional TargetGap target_gap = 30;
  
  optional uint32 await_clear_counter = 31 [default = 0];
  
  optional uint32 lane_change_unclear_counter = 32 [default = 0];
  
  optional uint32 efficiency_lane_change_counter = 33 [default = 0];

  optional uint32 lane_change_counter = 34 [default = 0];
  
  optional bool is_lane_change_success = 35 [default = false];

  optional bool prefer_suppress_lane_change = 36 [default = false];

  optional uint32 suppress_lane_change_counter = 37 [default = 0];

  optional double last_manual_timestamp = 38;

  optional double last_lc_terminate_by_paddle_timestamp = 39;
}

message CreepDeciderStatus {
  optional uint32 creep_clear_counter = 1;
}

message StopTime {
  optional string obstacle_id = 1;
  // the timestamp when start stopping for the crosswalk
  optional double stop_timestamp_sec = 2;
}

message CrosswalkStatus {
  optional string crosswalk_id = 1;
  // the timestamp when start stopping for the crosswalk
  repeated StopTime stop_time = 2;
  repeated string finished_crosswalk = 3;
}

message DestinationStatus {
  optional bool has_passed_destination = 1 [default = false];
}

message EmergencyStopStatus {
  optional apollo.common.PointENU stop_fence_point = 1;
}

message OpenSpaceStatus {
  repeated string partitioned_trajectories_index_history = 1;
  optional bool position_init = 2 [default = false];
}

message ParkAndGoStatus {
  optional apollo.common.PointENU adc_init_position = 1;
  optional double adc_init_heading = 2;
  optional bool in_check_stage = 3;
  optional apollo.common.PointENU adc_adjust_end_pose = 4;
}

message PathDeciderStatus {
  enum LaneBorrowDirection {
    LEFT_BORROW = 1;   // borrow left neighbor lane
    RIGHT_BORROW = 2;  // borrow right neighbor lane
  }
  optional int32 front_static_obstacle_cycle_counter = 1 [default = 0];
  optional int32 able_to_use_self_lane_counter = 2 [default = 0];
  optional bool is_in_path_lane_borrow_scenario = 3 [default = false];
  optional string front_static_obstacle_id = 4 [default = ""];
  repeated LaneBorrowDirection decided_side_pass_direction = 5;
  optional bool is_in_follow_creep_scenario = 6 [default = false];
  optional int32 follow_creep_counter = 7 [default = 0];
}

message PullOverStatus {
  enum PullOverType {
    PULL_OVER = 1;            // pull-over upon destination arrival
    EMERGENCY_PULL_OVER = 2;  // emergency pull-over
  }
  optional PullOverType pull_over_type = 1;
  optional bool plan_pull_over_path = 2 [default = false];
  optional apollo.common.PointENU position = 3;
  optional double theta = 4;
  optional double length_front = 5;
  optional double length_back = 6;
  optional double width_left = 7;
  optional double width_right = 8;
}

message ReroutingStatus {
  optional double last_rerouting_time = 1;
  optional bool reroute_required = 2 [default = false];
  optional bool reroute_succeeded = 3 [default = false];
  optional apollo.routing.RoutingRequest routing_request = 4;
  optional apollo.routing.NavigationInfo navigation_info = 5;
}

message SpeedDeciderStatus {
  repeated StopTime pedestrian_stop_time = 1;
}

message ScenarioStatus {
  optional apollo.planning.ScenarioType scenario_type = 1;
  optional apollo.planning.StageType stage_type = 2;
}

message StopSignStatus {
  optional string current_stop_sign_overlap_id = 1;
  optional string done_stop_sign_overlap_id = 2;
  repeated string wait_for_obstacle_id = 3;
}

//message TrafficLightStatus {
//  repeated string current_traffic_light_overlap_id = 1;
//  repeated string done_traffic_light_overlap_id = 2;
//}

message YieldSignStatus {
  repeated string current_yield_sign_overlap_id = 1;
  repeated string done_yield_sign_overlap_id = 2;
  repeated string wait_for_obstacle_id = 3;
}

message TurnSignalStatus {
  enum TurnSignalType {
    NONE = 1;
    LANE_TURN = 2;
    LANE_CHANGE = 3;
    ACROSS_BOUNDARY = 4;
    ACROSS_JUNCTION = 5;
  }
  enum Status {
    IN_LANE_ACROSS_BOUNDARY = 1; // ADC 2DBox in lane with trajectories across boundary 
    ON_ACROSS_BOUNDARY = 2; // ADC lies on lane boundary
    NOT_ACROSS_BOUNDARY = 3;
  }
  optional TurnSignalType turn_signal_type = 1 [default = NONE];
  optional apollo.common.VehicleSignal.TurnSignal turn_signal = 2 [default = TURN_NONE];
  optional Status across_status = 3 [default = NOT_ACROSS_BOUNDARY];
  optional string across_lane_id = 4;
  optional uint32 exit_across_counter = 5 [default = 0];
  optional uint32 action_counter = 6 [default = 0];
}

message IlqrPathOptimizerStatus {
  enum Status {
    NONE = 1;        
    BYPASS_STATIC_OBSTACLE_FROME_LEFT = 2; 
    BYPASS_STATIC_OBSTACLE_FROME_RIGHT = 3;      
    BYPASS_DYNAMIC_OBSTACLE_FROME_LEFT = 4;
    BYPASS_DYNAMIC_OBSTACLE_FROME_RIGHT = 5;
    NUDGE_STATIC_OBSTACLE = 6;    
    NUDGE_DYNAMIC_OBSTACLE = 7;   
  }
  optional Status status = 1;
}

message ObstacleHistoryStatus {
  repeated ObstacleHistory moving_obstacle_history = 1;
  repeated ObstacleHistory static_obstacle_history = 2;
}

message ObstacleHistory {
  optional string obstacle_id = 1;
  optional double obstacle_last_appear_time = 2;
  optional double obstacle_first_appear_time = 3;
  optional double obstacle_bypass_decision_last_appear_time = 4;
  optional int32 obstacle_last_bypass_decision = 5;
  optional bool is_overlap_obstacle = 6 [default = false];
}

message PlanningMode {
  enum PlanningModeType {
    NONE = 1;
    NOA = 2;
    LCC = 3;
  }
  optional PlanningModeType planning_mode = 1;
}

// note: please keep this one as minimal as possible. do NOT pollute it.
message PlanningStatus {
  optional BareIntersectionStatus bare_intersection = 1;
  optional ChangeLaneStatus change_lane = 2;
  optional CreepDeciderStatus creep_decider = 3;
  optional CrosswalkStatus crosswalk = 4;
  optional DestinationStatus destination = 5;
  optional EmergencyStopStatus emergency_stop = 6;
  optional OpenSpaceStatus open_space = 7;
  optional ParkAndGoStatus park_and_go = 8;
  optional PathDeciderStatus path_decider = 9;
  optional PullOverStatus pull_over = 10;
  optional ReroutingStatus rerouting = 11;
  optional ScenarioStatus scenario = 12;
  optional SpeedDeciderStatus speed_decider = 13;
  optional StopSignStatus stop_sign = 14;
  // optional TrafficLightStatus traffic_light = 15;
  optional YieldSignStatus yield_sign = 16;
  optional IlqrPathOptimizerStatus ilqr_path_optimizer = 17;
  optional ObstacleHistoryStatus obstacle_history = 18;
  optional TurnSignalStatus turn_signal = 19;
  optional PlanningMode planning_mode = 20;
}
