syntax = "proto2";

package apollo.planning_internal;

import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/basic_msgs/pnc_point.proto";
import "modules/common_msgs/chassis_msgs/chassis.proto";
import "modules/common_msgs/dreamview_msgs/chart.proto";
import "modules/common_msgs/localization_msgs/localization.proto";
import "modules/common_msgs/perception_msgs/traffic_light_detection.proto";
import "modules/common_msgs/planning_msgs/decision.proto";
import "modules/common_msgs/planning_msgs/navigation.proto";
import "modules/common_msgs/planning_msgs/scenario_type.proto";
import "modules/common_msgs/planning_msgs/sl_boundary.proto";
import "modules/common_msgs/routing_msgs/routing.proto";
import "modules/common_msgs/perception_msgs/ObjectFusion.proto";
import "modules/common_msgs/map_msgs/map_id.proto";
import "modules/common_msgs/prediction_msgs/efficient_lane_change_feature.proto";

message Debug {
  optional PlanningData planning_data = 2;
  optional IlqrDebug ilqr_debug = 3;
  optional ControlMode control_mode = 4;
}

message ControlMode {
  enum Mode {
    COMPLETE_MANUAL = 0;
    COMPLETE_AUTO_DRIVE = 1;
    AUTO_STEER_ONLY = 2;
    AUTO_SPEED_ONLY = 3;
    EMERGENCY_MODE = 4;
  }
  optional Mode mode = 1 [default = COMPLETE_MANUAL];
}

message SpeedPlan {
  optional string name = 1;
  repeated apollo.common.SpeedPoint speed_point = 2;
}

message StGraphBoundaryDebug {
  enum StBoundaryType {
    ST_BOUNDARY_TYPE_UNKNOWN = 1;
    ST_BOUNDARY_TYPE_STOP = 2;
    ST_BOUNDARY_TYPE_FOLLOW = 3;
    ST_BOUNDARY_TYPE_YIELD = 4;
    ST_BOUNDARY_TYPE_OVERTAKE = 5;
    ST_BOUNDARY_TYPE_KEEP_CLEAR = 6;
    ST_BOUNDARY_TYPE_DRIVABLE_REGION = 7;
  }
  optional string name = 1;
  repeated apollo.common.SpeedPoint point = 2;
  optional StBoundaryType type = 3;
  repeated apollo.common.SpeedPoint core_point = 4;
  optional double soft_expand_buffer = 5;
  repeated apollo.common.SpeedPoint buffer_point = 6;
}

message SLFrameDebug {
  optional string name = 1;
  repeated double sampled_s = 2;
  repeated double static_obstacle_lower_bound = 3;
  repeated double dynamic_obstacle_lower_bound = 4;
  repeated double static_obstacle_upper_bound = 5;
  repeated double dynamic_obstacle_upper_bound = 6;
  repeated double map_lower_bound = 7;
  repeated double map_upper_bound = 8;
  repeated apollo.common.SLPoint sl_path = 9;
  repeated double aggregated_boundary_s = 10;
  repeated double aggregated_boundary_low = 11;
  repeated double aggregated_boundary_high = 12;
}

message STGraphDebug {
  message STGraphSpeedConstraint {
    repeated double t = 1;
    repeated double lower_bound = 2;
    repeated double upper_bound = 3;
  }
  message STGraphKernelCuiseRef {
    repeated double t = 1;
    repeated double cruise_line_s = 2;
  }
  message STGraphKernelFollowRef {
    repeated double t = 1;
    repeated double follow_line_s = 2;
  }

  enum SpeedLimitType {
    INIT = 1;
    DRIVER = 2;
    KAPPA = 3;
    STOPLINE = 4;
    CROSSWALK = 5;
    NUDGE = 6;
    APPROACH_NAVI_END = 7;
    DKAPPA = 8;
    NARROW_ROAD = 9;
    UTURN = 10;
    UTURN_DKAPPA = 11;
    BEHAVIOR_MAP = 12;
    SMALL_MERGE_BIG_ROADS = 13;
  }
  enum BonusSpeedLimitType {
    EX_CROSSING = 1;
    EX_VRU = 2;
    EX_BLIND_SPOT = 3;
    EX_OTHERS = 4;
  }

  message SpeedLimitRef {
    optional double s = 1;
    optional double v = 2;
    optional SpeedLimitType basic_tag = 3;
    optional BonusSpeedLimitType bonus_tag = 4;
  }

  optional string name = 1;
  repeated StGraphBoundaryDebug boundary = 2;
  repeated apollo.common.SpeedPoint speed_limit = 3;
  repeated apollo.common.SpeedPoint speed_profile = 4;
  optional STGraphSpeedConstraint speed_constraint = 5;
  optional STGraphKernelCuiseRef kernel_cruise_ref = 6;
  optional STGraphKernelFollowRef kernel_follow_ref = 7;
  repeated apollo.common.SpeedPoint speed_reference = 8;
  repeated apollo.common.SpeedPoint path_speed_reference = 9;
  repeated apollo.common.SpeedPoint vt_speed_profile = 10;
  repeated SpeedLimitRef speed_limit_reference = 11;
  repeated apollo.common.SpeedPoint st_ilqr_reference = 12;
}

message SignalLightDebug {
  message SignalDebug {
    optional string light_id = 1;
    optional apollo.perception.TrafficLight.Color color = 2;
    optional bool blink = 3;
    optional double light_stop_s = 4;
    optional double adc_stop_deceleration = 5;
    optional bool is_stop_wall_created = 6;
    optional double minimum_pass_time = 7;
    optional double uncertainty = 8 [default = 0.0];
  }
  optional double adc_speed = 1;
  optional double adc_front_s = 2;
  repeated SignalDebug signal = 3;
}

message DecisionTag {
  optional string decider_tag = 1;
  optional apollo.planning.ObjectDecisionType decision = 2;
}

message ObstacleDebug {
  enum RWLevel {
    RW_LOWEST = 1;
    RW_LOW = 2;
    RW_MIDDLE = 3;
    RW_HIGH = 4;
    RW_HIGHEST = 5;
  }
  enum IntentionType {
    INN_JUNCTION = 1;
    OUT_JUNCTION = 2;
    IN_OUT_JUNCTION = 3;
    M2N = 4;
    ROUNDABOUT = 5;
    STOP_SIGN = 6;
    T_INTERSECTION = 7;
    MERGE_SPLIT = 8;
  };
  optional string id = 1;
  optional apollo.planning.SLBoundary sl_boundary = 2;
  repeated DecisionTag decision_tag = 3;
  repeated double vertices_x_coords = 4;
  repeated double vertices_y_coords = 5;
  optional bool is_dead_car = 6;
  optional RWLevel intention_road_right = 7;
  optional IntentionType obs_intention_type = 8;
  optional IntentionType adc_intention_type = 9;
}

message ReferenceLineDebug {
  optional string id = 1;
  optional double length = 2;
  optional double cost = 3;
  optional bool is_change_lane_path = 4;
  optional bool is_drivable = 5;
  optional bool is_protected = 6;
  optional bool is_offroad = 7;
  optional double minimum_boundary = 8;
  optional double average_kappa = 9 [deprecated = true];
  optional double average_dkappa = 10 [deprecated = true];
  optional double kappa_rms = 11;
  optional double dkappa_rms = 12;
  optional double kappa_max_abs = 13;
  optional double dkappa_max_abs = 14;
  optional double average_offset = 15;
  optional double eff_lc_nn_model_prob = 16;
  optional double eff_lc_replace_cost = 17;
  optional double eff_lc_total_cost = 18;
}

message SampleLayerDebug {
  repeated apollo.common.SLPoint sl_point = 1;
}

message DpPolyGraphDebug {
  repeated SampleLayerDebug sample_layer = 1;
  repeated apollo.common.SLPoint min_cost_point = 2;
}

message ScenarioDebug {
  optional apollo.planning.ScenarioType scenario_type = 1;
  optional apollo.planning.StageType stage_type = 2;
  optional string msg = 3;
}

message Trajectories {
  repeated apollo.common.Trajectory trajectory = 1;
}

message OpenSpaceDebug {
  optional apollo.planning_internal.Trajectories trajectories = 1;
  optional apollo.common.VehicleMotion warm_start_trajectory = 2;
  optional apollo.common.VehicleMotion smoothed_trajectory = 3;
  repeated double warm_start_dual_lambda = 4;
  repeated double warm_start_dual_miu = 5;
  repeated double optimized_dual_lambda = 6;
  repeated double optimized_dual_miu = 7;
  repeated double xy_boundary = 8;
  repeated apollo.planning_internal.ObstacleDebug obstacles = 9;
  optional apollo.common.TrajectoryPoint roi_shift_point = 10;
  optional apollo.common.TrajectoryPoint end_point = 11;
  optional apollo.planning_internal.Trajectories partitioned_trajectories = 12;
  optional apollo.planning_internal.Trajectories chosen_trajectory = 13;
  optional bool is_fallback_trajectory = 14;
  optional apollo.planning_internal.Trajectories fallback_trajectory = 15;
  optional apollo.common.TrajectoryPoint trajectory_stitching_point = 16;
  optional apollo.common.TrajectoryPoint future_collision_point = 17;
  optional double time_latency = 18 [default = 0.0];  // ms
  optional apollo.common.PointENU origin_point = 19;  // meter
  optional double origin_heading_rad = 20;
}

message SmootherDebug {
  enum SmootherType {
    SMOOTHER_NONE = 1;
    SMOOTHER_CLOSE_STOP = 2;
  }
  optional bool is_smoothed = 1;

  optional SmootherType type = 2 [default = SMOOTHER_NONE];
  optional string reason = 3;
}

message PullOverDebug {
  optional apollo.common.PointENU position = 1;
  optional double theta = 2;
  optional double length_front = 3;
  optional double length_back = 4;
  optional double width_left = 5;
  optional double width_right = 6;
}

// next ID: 30
message PlanningData {
  // input
  optional apollo.localization.LocalizationEstimate adc_position = 7;
  optional apollo.a_canbus.Chassis chassis = 8;
  optional apollo.routing.RoutingResponse routing = 9;
  optional apollo.common.TrajectoryPoint init_point = 10;

  repeated apollo.common.Path path = 6;

  repeated SpeedPlan speed_plan = 13;
  repeated STGraphDebug st_graph = 14;
  repeated SLFrameDebug sl_frame = 15;

  optional apollo.common.Header prediction_header = 16;
  optional SignalLightDebug signal_light = 17;

  repeated ObstacleDebug obstacle = 18;
  repeated ReferenceLineDebug reference_line = 19;
  optional DpPolyGraphDebug dp_poly_graph = 20;
  optional LatticeStTraining lattice_st_image = 21;
  optional apollo.relative_map.MapMsg relative_map = 22;
  optional AutoTuningTrainingData auto_tuning_training_data = 23;
  optional double front_clear_distance = 24;
  repeated apollo.dreamview.Chart chart = 25;
  optional ScenarioDebug scenario = 26;
  optional OpenSpaceDebug open_space = 27;
  optional SmootherDebug smoother = 28;
  optional PullOverDebug pull_over = 29;
  optional HybridModelDebug hybrid_model = 30;
  optional double prediction_timestamp = 31;
  optional double perception_timestamp = 32;
  optional MultiPolicyDebug multi_policy_debug = 33;
}

message LatticeStPixel {
  optional int32 s = 1;
  optional int32 t = 2;
  optional uint32 r = 3;
  optional uint32 g = 4;
  optional uint32 b = 5;
}

message LatticeStTraining {
  repeated LatticeStPixel pixel = 1;
  optional double timestamp = 2;
  optional string annotation = 3;
  optional uint32 num_s_grids = 4;
  optional uint32 num_t_grids = 5;
  optional double s_resolution = 6;
  optional double t_resolution = 7;
}

message CostComponents {
  repeated double cost_component = 1;
}

message AutoTuningTrainingData {
  optional CostComponents teacher_component = 1;
  optional CostComponents student_component = 2;
}

message CloudReferenceLineRequest {
  repeated apollo.routing.LaneSegment lane_segment = 1;
}

message CloudReferenceLineRoutingRequest {
  optional apollo.routing.RoutingResponse routing = 1;
}

message CloudReferenceLineResponse {
  repeated apollo.common.Path segment = 1;
}

message HybridModelDebug {
  optional bool using_learning_model_output = 1 [default = false];
  optional double learning_model_output_usage_ratio = 2;
  optional string learning_model_output_fail_reason = 3;
  optional apollo.common.Path evaluated_path_reference = 4;
}

message IlqrDebug {
  message State {
    optional double x = 1;
    optional double y = 2;
    optional double p = 3;
    optional double k = 4;
    optional double q = 5;
  }
  repeated State init_path = 1;
  repeated State target_path = 2;
  repeated State ilqr_path = 3;
  optional bool converge = 4;
  optional int32 iteration_count = 5;
  optional int32 line_search_count = 6;
  optional double time_spend = 7;
  message Position {
    optional double x = 1;
    optional double y = 2;
  }
  message Obstacle{
    optional string id = 1;
    optional Position position = 2;
    repeated Position corner = 3;
    repeated Position expand_corner = 4;
    enum Label {
      IGNORE = 1;
      LEFT = 2;
      RIGHT = 3;
    }
    optional Label label = 5;
  }
  repeated Obstacle obstacles = 8;
  repeated Position left_boundary = 9;
  repeated Position right_boundary = 10;
  message Line {
    repeated Position points = 1;
  }
  repeated Line left_shoulders = 11;
  repeated Line right_shoulders = 12;
  repeated robosense.perception_msgs.ObjectFusion post_fusion_perception_obstacle = 13;
  message shoulder_info {
    optional hdmap.Id id = 1;
    optional bool is_left = 2;
  }
  repeated shoulder_info shoulders = 14;
  enum LonBehavior {
    STOP = 1;
    GO = 2;
    CUTIN = 3;
    DEFAULT = 4;
    FALLBACK = 5;
    CUTOUT = 6;
  }
  optional LonBehavior lon_behavior = 15;
  optional double min_distance_to_obstacles = 16;
  optional double min_distance_to_left_shoulders = 17;
  optional double min_distance_to_right_shoulders = 18;
  optional bool enable_insensitive_parameters = 19 [default = false];
  optional bool is_emergency = 101;
  optional double emergency_nudge_distance = 102;
  optional double emergency_nudge_compressed_distance = 103;
  repeated string emergency_obstacles = 104;
  optional string emergency_trigger_info = 105;
  optional apollo.prediction.EfficientLaneSequence efficient_lane_seq = 106;
  repeated double each_path_point_lateral_space = 107;
  optional double min_path_lateral_space = 108;
  repeated double each_path_point_left_lateral_space = 109;
  repeated double each_path_point_right_lateral_space = 110;
  optional bool is_escape_scene = 111 [default = false];
  optional bool is_u_turn = 112 [default = false];
  optional uint64 bypass_number = 113 [default = 0];
  message InteractionObstacle {
    optional uint32 id = 1;
    enum Type {
      Lateral = 0;
      Longitudinal_Yield = 1;
      Longitudinal_Follow = 2;
      HighROW = 3;
    };
    optional Type type = 2;
    optional uint32 rank = 3;
  }
  repeated InteractionObstacle interaction_obstacle = 114;
  optional bool is_follow_mode = 115;
}

message MultiPolicyDebug {
  // optiml_scene
  repeated apollo.common.Path forward_path = 1;
  optional Policy optimal_policy = 2;

  // all scenes debug
  repeated SceneDebug scenes = 3;

  repeated SpacetimeObs spacetime_obs = 4;
}

message SpacetimeObs {
  optional string obs_id = 1;
  optional uint32 traj_id = 2;
}

message SceneDebug {
  optional Policy ego_policy = 1;
  optional double probability = 2;
  optional Policycost scene_cost = 3;
  // ego path
  optional apollo.common.Path ego_forward_path = 4;
  // lane-change gap id
  optional string gap_front_obs_id = 5;
  optional string gap_rear_obs_id = 6;
  optional bool is_u_turn = 7 [default = false];
}

message Policycost {
  optional double scene_cost = 1;
  optional double safety_cost = 2;
  optional double efficiency_cost = 3;
  optional double navigation_cost = 4;
  optional double uncertainty_cost = 5;
}

message Policy {
  enum LatAction {
    LANE_KEEPING = 1;
    LANE_CHANGE_LEFT = 2;
    LANE_CHANGE_RIGHT = 3;
    LATUNKNOW = 4;
  }
  enum LonAction {
    STOP = 1;
    MAINTAIN = 2;
    ACCELERATE = 3;
    DECELERATE = 4;
    LONUNKNOW = 5;
  }
  optional LatAction lat_action = 1 [default = LATUNKNOW];
  optional LonAction lon_action = 2 [default = LONUNKNOW];
}