syntax = "proto2";

package apollo.planning;


enum ScenarioType {
    LANE_FOLLOW = 0;  // default scenario
    E2E = 1;

    // intersection involved
    BARE_INTERSECTION_UNPROTECTED = 2;
    STOP_SIGN_PROTECTED = 3;
    STOP_SIGN_UNPROTECTED = 4;
    TRAFFIC_LIGHT_PROTECTED = 5;
    TRAFFIC_LIGHT_UNPROTECTED_LEFT_TURN = 6;
    TRAFFIC_LIGHT_UNPROTECTED_RIGHT_TURN = 7;
    YIELD_SIGN = 8;

    // parking
    PULL_OVER = 9;
    VALET_PARKING = 10;

    EMERGENCY_PULL_OVER = 11;
    EMERGENCY_STOP = 12;

    // misc
    NARROW_STREET_U_TURN = 13;
    PARK_AND_GO = 14;

    // learning model sample
    LEARNING_MODEL_SAMPLE = 15;
    // turn around
    DEADEND_TURNAROUND = 16;

    // multi-policy
    MULTIPOLICY = 17;
}

// StageType is a superset of stages from all scenarios.
// It is created to keep different scenarios have uniform config interface
enum StageType {
    NO_STAGE = 0;

    LANE_FOLLOW_DEFAULT_STAGE = 1;
    E2E_DEFAULT_STAGE = 2;

    // bare_intersection_unprotected scenario
    BARE_INTERSECTION_UNPROTECTED_APPROACH = 200;
    BARE_INTERSECTION_UNPROTECTED_INTERSECTION_CRUISE = 201;

    // stop_sign_unprotected scenario
    STOP_SIGN_UNPROTECTED_PRE_STOP = 300;
    STOP_SIGN_UNPROTECTED_STOP = 301;
    STOP_SIGN_UNPROTECTED_CREEP = 302;
    STOP_SIGN_UNPROTECTED_INTERSECTION_CRUISE = 303;

    // traffic_light_protected scenario
    TRAFFIC_LIGHT_PROTECTED_APPROACH = 400;
    TRAFFIC_LIGHT_PROTECTED_INTERSECTION_CRUISE = 401;

    // traffic_light_unprotected_left_turn scenario
    TRAFFIC_LIGHT_UNPROTECTED_LEFT_TURN_APPROACH = 410;
    TRAFFIC_LIGHT_UNPROTECTED_LEFT_TURN_CREEP = 411;
    TRAFFIC_LIGHT_UNPROTECTED_LEFT_TURN_INTERSECTION_CRUISE = 412;

    // traffic_light_unprotected_right_turn scenario
    TRAFFIC_LIGHT_UNPROTECTED_RIGHT_TURN_STOP = 420;
    TRAFFIC_LIGHT_UNPROTECTED_RIGHT_TURN_CREEP = 421;
    TRAFFIC_LIGHT_UNPROTECTED_RIGHT_TURN_INTERSECTION_CRUISE = 422;

    // pull_over scenario
    PULL_OVER_APPROACH = 500;
    PULL_OVER_RETRY_APPROACH_PARKING = 501;
    PULL_OVER_RETRY_PARKING = 502;

    // emergency_pull_over scenario
    EMERGENCY_PULL_OVER_SLOW_DOWN = 600;
    EMERGENCY_PULL_OVER_APPROACH = 601;
    EMERGENCY_PULL_OVER_STANDBY = 602;

    // emergency_pull_over scenario
    EMERGENCY_STOP_APPROACH = 610;
    EMERGENCY_STOP_STANDBY = 611;

    // valet parking scenario
    VALET_PARKING_APPROACHING_PARKING_SPOT = 700;
    VALET_PARKING_PARKING = 701;

    // turning around scenario
    DEADEND_TURNAROUND_APPROACHING_TURNING_POINT = 1100;
    DEADEND_TURNAROUND_TURNING = 1101;

    // park_and_go scenario
    PARK_AND_GO_CHECK = 800;
    PARK_AND_GO_CRUISE = 801;
    PARK_AND_GO_ADJUST = 802;
    PARK_AND_GO_PRE_CRUISE = 803;

    // yield_sign scenario
    YIELD_SIGN_APPROACH = 900;
    YIELD_SIGN_CREEP = 901;

    // learning_model_sample scenario
    LEARNING_MODEL_RUN = 1000;
}
