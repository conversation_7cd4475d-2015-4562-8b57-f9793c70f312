syntax = "proto2";
package apollo.planning;

import "modules/common_msgs/basic_msgs/header.proto";


message PadMessage {
  optional apollo.common.Header header = 1;

  enum DrivingAction {
    NONE = 100;
    FOLLOW = 0;
    CHANGE_LEFT = 1;
    CHANGE_RIGHT = 2;
    PULL_OVER = 3;
    STOP = 4;
    RESUME_CRUISE = 5;
  };

  // driving action
  optional DrivingAction action = 2;

  enum PnCTestingMode {
    OPEN_LOOP = 0;
    PLANNING_LOOP = 1;
    CLOSE_LOOP = 2;
    ONLINE = 3;
  }

  // pnc testing mode
  optional PnCTestingMode mode = 3;


  message PlanningSimulation {
    optional bool path_use_curb = 1 [default = true];
    optional bool path_use_occupancy = 2 [default = true];
    optional bool path_use_solidline = 3 [default = true];
  }

  optional PlanningSimulation path_planning_mode = 4;
}
