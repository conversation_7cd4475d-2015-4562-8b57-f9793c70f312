syntax = "proto2";

package apollo.planning;

import "modules/common_msgs/chassis_msgs/chassis.proto";
import "modules/common_msgs/basic_msgs/drive_state.proto";
import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/basic_msgs/pnc_point.proto";
import "modules/common_msgs/map_msgs/map_id.proto";
import "modules/common_msgs/planning_msgs/decision.proto";
import "modules/common_msgs/planning_msgs/planning_internal.proto";
import "modules/common_msgs/planning_msgs/planning_status.proto";
import "modules/common_msgs/prediction_msgs/efficient_lane_change_feature.proto";

message EStop {
  // is_estop == true when emergency stop is required
  optional bool is_estop = 1;
  optional string reason = 2;
}

message TaskStats {
  optional string name = 1;
  optional double time_ms = 2;
}

message LatencyStats {
  optional double total_time_ms = 1;
  repeated TaskStats task_stats = 2;
  optional double init_frame_time_ms = 3;
}

enum JucType {
  UNKNOWN = 0;
  IN_ROAD = 1;
  CROSS_ROAD = 2;
  FORK_ROAD = 3;
  MAIN_SIDE = 4;
  DEAD_END = 5;
}

message RSSInfo {
  optional bool is_rss_safe = 1;
  optional double cur_dist_lon = 2;
  optional double rss_safe_dist_lon = 3;
  optional double acc_lon_range_minimum = 4;
  optional double acc_lon_range_maximum = 5;
  optional double acc_lat_left_range_minimum = 6;
  optional double acc_lat_left_range_maximum = 7;
  optional double acc_lat_right_range_minimum = 8;
  optional double acc_lat_right_range_maximum = 9;
}

message NavigationLaneChangeInfo {
  optional bool is_left_change_triggered = 1 [default = false];
  optional bool is_right_change_triggered = 2 [default = false];
}

message ReferenceLane {
  optional string name = 1;  // "origin_reference_lane" or "target_reference_lane"
  repeated apollo.common.PathPoint path_point = 2;
  optional bool fallback = 3 [default = false];  // true 说明该 ReferenceLane 优先上色【异常颜色】
  optional bool in_use = 4 [default = false];    // true 说明该 ReferenceLane 默认上色【常规颜色】，否则默认上色【候选颜色】
}

message ReferenceTrajectory {
  optional string name = 1;  // "origin_reference_trajectory" or "target_reference_trajectory"
  repeated apollo.common.PathPoint path_point = 2;
}

// next id: 24
message ADCTrajectory {
  optional apollo.common.Header header = 1;

  optional double total_path_length = 2;  // in meters

  optional double total_path_time = 3;    // in seconds

  repeated ReferenceLane reference_lane = 4;

  repeated ReferenceTrajectory reference_trajectory = 5;

  optional EStop estop = 6;

  optional apollo.planning_internal.Debug debug = 8;

  // is_replan == true mean replan triggered
  optional bool is_replan = 9 [default = false];

  // Specify trajectory gear
  optional apollo.a_canbus.Chassis.GearPosition gear = 10;

  // path data + speed data
  repeated apollo.common.TrajectoryPoint trajectory_point = 12;

  // path point without speed info
  repeated apollo.common.PathPoint path_point = 13;

  optional apollo.planning.DecisionResult decision = 14;

  optional LatencyStats latency_stats = 15;

  // the routing used for current planning result
  optional apollo.common.Header routing_header = 16;
  enum RightOfWayStatus {
    UNPROTECTED = 0;
    PROTECTED = 1;
  }
  optional RightOfWayStatus right_of_way_status = 17;

  // lane id along current reference line
  repeated apollo.hdmap.Id lane_id = 18;

  // set the engage advice for based on current planning result.
  optional apollo.common.EngageAdvice engage_advice = 19;

  // the region where planning cares most
  message CriticalRegion {
    repeated apollo.common.Polygon region = 1;
  }
  // critical region will be empty when planning is NOT sure which region is
  // critical
  // critical regions may or may not overlap
  optional CriticalRegion critical_region = 20;

  enum TrajectoryType {
    UNKNOWN = 0;
    NORMAL = 1;
    PATH_FALLBACK = 2;
    SPEED_FALLBACK = 3;
    PATH_REUSED = 4;
  }
  optional TrajectoryType trajectory_type = 21 [default = UNKNOWN];

  optional string replan_reason = 22;

  // lane id along target reference line
  repeated apollo.hdmap.Id target_lane_id = 23;

  // complete dead end flag
  optional bool car_in_dead_end = 24;

  // expectation speed
  optional float expectation_speed = 25 [default = 0.];

  // relative path data + speed data
  repeated apollo.common.TrajectoryPoint relative_trajectory_point = 27;

  // lat error on map 
  optional double lat_error_to_lane_center = 28;

  // navigation lane change info
  optional NavigationLaneChangeInfo navi_lane_change_info = 29;

  // human lane preference
  optional apollo.a_canbus.DriverPreference driver_perference = 30;

  // output related to RSS
  optional RSSInfo rss_info = 31;

  optional apollo.core_planning.PlanningStatus planning_status = 32;

  optional string eff_lc_nn_info = 33;

  optional bool is_ready_to_auto_drive = 34 [default = true];

  optional double worldmodel_timestamp = 35;

  optional double lidar_timestamp = 36;

  optional double navi_length = 37;

  optional apollo.prediction.ActionType ego_path_proposal_action = 38;

  optional int32 ego_path_proposal_path_index = 39 [default = -1];
}

// next id: 14
message ADCPlanningTrajectory {
  optional apollo.common.Header header = 1;

  optional EStop estop = 2;

  // is_replan == true mean replan triggered
  optional bool is_replan = 3 [default = false];

  // Specify trajectory gear
  optional apollo.a_canbus.Chassis.GearPosition gear = 4;

  // path data + speed data
  repeated apollo.common.TrajectoryPoint trajectory_point = 5;

  // path point without speed info
  repeated apollo.common.PathPoint path_point = 6;

  optional apollo.planning.DecisionResult decision = 7;

  optional ADCTrajectory.TrajectoryType trajectory_type = 8 [default = UNKNOWN];

  // expectation speed
  optional float expectation_speed = 9 [default = 0.];

  optional double perception_timestamp = 10;

  optional apollo.planning_internal.ControlMode control_mode = 11;

  optional apollo.planning_internal.IlqrDebug.LonBehavior lon_behavior = 12;
  optional double min_distance_to_obstacles = 13;
  optional double min_distance_to_left_shoulders = 14;
  optional double min_distance_to_right_shoulders = 15;

  optional bool enable_insensitive_parameters = 16 [default = false];
  optional double min_path_lateral_space = 17;
  repeated double each_path_point_lateral_space = 18;
  repeated apollo.common.PathPoint ilqr_path_point = 19;
  repeated double each_path_point_left_lateral_space = 20;
  repeated double each_path_point_right_lateral_space = 21;
  optional bool is_escape_scene = 22 [default = false];
  optional double min_static_longitudinal_space = 23 [default = 100.];

  optional double worldmodel_timestamp = 24;
  optional double lidar_timestamp = 25;

  optional bool is_u_turn = 26 [default = false];

  optional apollo.core_planning.PlanningStatus planning_status = 27;

  optional double navi_length = 28;

  // repeated apollo.common.Path path = 16;  // TODO Eliminate it
}
