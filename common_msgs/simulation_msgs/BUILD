## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "scenario_proto",
    srcs = ["scenario.proto"],
    deps = [
        ":agent_proto",
        ":grading_metric_proto",
        "//modules/common_msgs/perception_msgs:traffic_light_detection_proto",
        "//modules/common_msgs/planning_msgs:pad_msg_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

proto_library(
    name = "grading_metric_proto",
    srcs = ["grading_metric.proto"],
    deps = [
        ":grading_condition_proto",
    ],
)

proto_library(
    name = "agent_proto",
    srcs = ["agent.proto"],
)

proto_library(
    name = "grading_condition_proto",
    srcs = ["grading_condition.proto"],
    deps = [
        "//modules/common_msgs/map_msgs:map_geometry_proto",
    ],
)

apollo_package()
