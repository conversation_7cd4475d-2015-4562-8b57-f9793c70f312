## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "map_stop_sign_proto",
    srcs = ["map_stop_sign.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_lane_proto",
    srcs = ["map_lane.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_proto",
    srcs = ["map.proto"],
    deps = [
        ":map_clear_area_proto",
        ":map_crosswalk_proto",
        ":map_junction_proto",
        ":map_lane_proto",
        ":map_overlap_proto",
        ":map_parking_space_proto",
        ":map_pnc_junction_proto",
        ":map_road_proto",
        ":map_rsu_proto",
        ":map_signal_proto",
        ":map_speed_bump_proto",
        ":map_stop_sign_proto",
        ":map_yield_sign_proto",
    ],
)

proto_library(
    name = "map_rsu_proto",
    srcs = ["map_rsu.proto"],
    deps = [
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_yield_sign_proto",
    srcs = ["map_yield_sign.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_clear_area_proto",
    srcs = ["map_clear_area.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_crosswalk_proto",
    srcs = ["map_crosswalk.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_pnc_junction_proto",
    srcs = ["map_pnc_junction.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_speed_control_proto",
    srcs = ["map_speed_control.proto"],
    deps = [
        ":map_geometry_proto",
    ],
)

proto_library(
    name = "map_speed_bump_proto",
    srcs = ["map_speed_bump.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_road_proto",
    srcs = ["map_road.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_junction_proto",
    srcs = ["map_junction.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_id_proto",
    srcs = ["map_id.proto"],
)

proto_library(
    name = "map_signal_proto",
    srcs = ["map_signal.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
    ],
)

proto_library(
    name = "map_overlap_proto",
    srcs = ["map_overlap.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

proto_library(
    name = "map_geometry_proto",
    srcs = ["map_geometry.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:geometry_proto",
    ],
)

proto_library(
    name = "map_parking_space_proto",
    srcs = ["map_parking_space.proto"],
    deps = [
        ":map_geometry_proto",
        ":map_id_proto",
    ],
)

apollo_package()
