## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "v2x_traffic_light_proto",
    srcs = ["v2x_traffic_light.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:direction_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

apollo_package()
