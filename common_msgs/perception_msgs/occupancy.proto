syntax = "proto2";

package apollo.perception;

import "modules/common_msgs/basic_msgs/header.proto";

enum OCCUPIED_TYPE {
    FREE = 0;
    CURB = 1;
    OCCUPIED = 2;
}

// given a point (x,y), check the occupied status of this point:
//   row = (x_max - x) / resolution
//   col = (y_max - y) / resolution
//   index = row * width + col
// if value[index] equals to 0, then this point is free
message Occupancy {
    optional apollo.common.Header header = 1;             // Header
    repeated int32 value = 2;
    required int32 width = 3;
    required int32 height = 4;
    required double x_max = 5;
    required double y_max = 6;
    required double resolution = 7;
}
