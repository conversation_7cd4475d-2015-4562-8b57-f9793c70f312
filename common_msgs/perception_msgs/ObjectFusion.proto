syntax = "proto2";  

package robosense.perception_msgs;

import "modules/common_msgs/perception_msgs/Vec3D.proto";
import "modules/common_msgs/perception_msgs/Vec2D.proto";
import "modules/common_msgs/perception_msgs/Size3D.proto";
import "modules/common_msgs/perception_msgs/MotionType.proto";
import "modules/common_msgs/perception_msgs/ObjectType.proto";

message ObjectFusion
{
    optional uint32 track_id=1;
    optional robosense.perception_msgs.ObjectType type=2;
    optional float type_confidence=3;
    optional robosense.perception_msgs.Vec3D box_center_base=4;
    optional robosense.perception_msgs.Vec3D box_center_odom=5;
    optional float yaw_base=6;
    optional float yaw_odom=7;
    optional robosense.perception_msgs.Size3D box_size=8;
    optional float yaw_rate=9;
    optional robosense.perception_msgs.Vec3D velocity_base=10;
    optional robosense.perception_msgs.Vec3D velocity_odom=11;
    optional robosense.perception_msgs.Vec3D acceleration_base=12;
    optional robosense.perception_msgs.Vec3D acceleration_odom=13;
    optional robosense.perception_msgs.MotionType motion_type=14;
    optional uint32 track_state=15;
    optional uint32 track_age=16;
    optional uint32 exist_confidence=17;
    optional uint32 signal_light=18;
    optional uint32 brake_light=19;
    repeated float cutin_states=20;
    optional uint32 door_state=21;
    repeated robosense.perception_msgs.Vec2D polygon_base=22;
    repeated robosense.perception_msgs.Vec2D polygon_odom=23;
    repeated uint32 extended_key=24;
    repeated float extended_value=25;
}
