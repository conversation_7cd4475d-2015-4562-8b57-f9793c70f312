syntax = "proto2";

package apollo.perception;

import "modules/common_msgs/basic_msgs/header.proto";

message KeyPoint {
    optional double x = 1;
    optional double y = 2;
    optional double z = 3;
    optional double heading = 4;
}

message CenterLine {
    repeated KeyPoint key_points = 1;
}

enum LineType {
    CROSSABLE = 0;
    UNCROSSABLE = 1;
}

message Boundary {
    repeated KeyPoint key_points = 1;
    repeated double distance_to_center = 2;
    optional LineType line_type = 3;
}

enum LaneDirection {
    STRAIGHT = 0;
    LEFT_TRUN = 1;
    STRAIGHT_OR_LEFT_TURN = 2;
    RIGHT_TURN = 3;
}

message Lane {
    // tracker_id, not postion index
    required int32 tracker_id = 1;
    required CenterLine center_line = 2;
    required Boundary left_boundary = 3;
    required Boundary right_boundary = 4;
    optional LaneDirection lane_direction = 5;
    // preference from perception
    optional double perception_preference = 6;
    // preference form LP 
    optional double preference = 7;
    required int32 lane_index = 8;
}

message RoadStructure {
    optional apollo.common.Header header = 1;             // Header
    repeated Lane lane_set = 2;
}
