syntax = "proto2";

package robosense.rs_perception.trafficlight;

enum Color {
  UNKNOWN = 0;
  RED = 1;
  RED_FLASH = 2;
  YELLOW = 3;
  YELLOW_FLASH = 4;
  GREEN = 5;
  GREEN_FLASH = 6;
  BLACK = 7;
  INVALID = 20;
};

enum Shape {
  SHAPE_UNKNOWN = 0;
  CIRCLE = 1;
  UP_ARROW = 2;
  DOWN_ARROW = 3;
  LEFT_ARROW = 4;
  RIGHT_ARROW = 5;
  UP_LEFT_ARROW = 6;
  UP_RIGHT_ARROW = 7;
  TURN_ROUND_ARROW = 8;
  LEFT_TURN_ROUND_ARROW = 9;
  NUMBER = 10;
  FORBID = 11;
  PEDESTRIAN = 12;
  BICYCLE = 13;
  TEXT = 14;
  OTHERS = 15;

  CIRCLE_TO_LEFT_ARROW = 16;
  CIRCLE_TO_UTURN_ARROW = 17;
  CIRCLE_TO_UP_ARROW = 18;
  CIRCLE_TO_RIGHT_ARROW = 19;

  LEFT_TO_UTURN_ARROW = 20;
  LEFT_TO_UP_ARROW = 21;
  LEFT_TO_RIGHT_ARROW = 22;
  LEFT_TO_CIRCLE = 23;

  UP_TO_LEFT_ARROW = 24;
  UP_TO_UTURN_ARROW = 25;
  UP_TO_RIGHT_ARROW = 26;
  UP_TO_CIRCLE = 27;

  UTURN_TO_LEFT_ARROW = 28;
  UTURN_TO_UP_ARROW = 29;
  UTURN_TO_RIGHT_ARROW = 30;
  UTURN_TO_CIRCLE = 31;

  RIGHT_TO_LEFT_ARROW = 32;
  RIGHT_TO_UTURN_ARROW = 33;
  RIGHT_TO_UP_ARROW = 34;
  RIGHT_TO_CIRCLE = 35;
};

enum CameraID {
  CAMERA_FRONT_NARROW = 0;
  CAMERA_FRONT_WIDE = 1;
  CAMERA_LEFT_FRONT_PINHOLE = 2;
  CAMERA_RIGHT_FRONT_PINHOLE = 3;
};

enum Source {
  MAP = 0;
  PERCEPTION = 1;
};

enum NaviType {
  FORWARD = 0;
  TURN_LEFT = 1;
  TURN_RIGHT = 2;
  UTURN_LEFT = 3;
  FORWARD_LEFT = 4;
  FORWARD_RIGHT = 5;
  DUMMY_FORWARD = 6; // 不参与匹配的冗余灯, 解上海冗余灯问题的临时方案
};

enum GREEN_FLASH_TIME_STATE {
  GREEN_FLASH_TIME_STATE_VALID = 0;
  GREEN_FLASH_TIME_STATE_INVALID = 1;
}

enum YELLOW_TIME_STATE {
  YELLOW_TIME_STATE_VALID = 0;
  YELLOW_TIME_STATE_INVALID = 1;
}

enum MapMode {
  NO_MAP = 0;
  HDMAP = 1;
  LANEMAP = 2;
  FUSION = 3;
}
