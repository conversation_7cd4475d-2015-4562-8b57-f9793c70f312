syntax = "proto2";

package apollo.perception;

import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/perception_msgs/TrafficlightTypes.proto";

message TrafficLightBox {
  optional int32 x = 1;
  optional int32 y = 2;
  optional int32 width = 3;
  optional int32 height = 4;
  optional TrafficLight.Color color = 5;
  optional bool selected = 6;
  optional string camera_name = 7;
}

message PointInEgo {
  optional double x = 1;
  optional double y = 2;
  optional double z = 3;
}


message TrafficLightDebug {
  optional TrafficLightBox cropbox = 1;
  repeated TrafficLightBox box = 2;
  optional int32 signal_num = 3;
  optional int32 valid_pos = 4;
  optional double ts_diff_pos = 5;
  optional double ts_diff_sys = 6;
  optional int32 project_error = 7;
  optional double distance_to_stop_line = 8;
  optional int32 camera_id = 9 [deprecated = true];
  repeated TrafficLightBox crop_roi = 10;
  repeated TrafficLightBox projected_roi = 11;
  repeated TrafficLightBox rectified_roi = 12;
  repeated TrafficLightBox debug_roi = 13;
}

message TrafficLight {
  enum Color {
    UNKNOWN = 0;
    RED = 1;
    YELLOW = 2;
    GREEN = 3;
    BLACK = 4;
  };
  optional Color color = 1;

  // Traffic light string-ID in the map data.
  optional string id = 2;

  // How confidence about the detected results, between 0 and 1.
  optional double confidence = 3 [default = 1.0];

  // Duration of the traffic light since detected.
  optional double tracking_time = 4;

  // Is traffic blinking
  optional bool blink = 5;

  // v2x traffic light remaining time.
  optional double remaining_time = 6;

  repeated PointInEgo corner_points = 7;

  // yellow color is from green, and yellow time will be useful
  optional bool green2yellow = 8;

  // the time from green to yellow, default 0
  optional double green2yellow_time = 9;

  // unknown of occlusion
  optional bool occlusion = 10;

  // green flash time: first flash time always, when green flash
  optional robosense.rs_perception.trafficlight.GREEN_FLASH_TIME_STATE green_flash_time_state = 11;
  optional double green_flash_time = 12;
  // yellow time: first yellow time always, when yellow
  optional robosense.rs_perception.trafficlight.YELLOW_TIME_STATE yellow_time_state = 13;
  optional double yellow_time = 14;
}

message TrafficLightDetection {
  optional apollo.common.Header header = 2;
  repeated TrafficLight traffic_light = 1;
  optional TrafficLightDebug traffic_light_debug = 3;
  optional bool contain_lights = 4;
  enum CameraID {
    CAMERA_FRONT_LONG = 0;
    CAMERA_FRONT_NARROW = 1;
    CAMERA_FRONT_SHORT = 2;
    CAMERA_FRONT_WIDE = 3;
  };
  optional CameraID camera_id = 5;
  optional double debug_timestamp = 6;
}
