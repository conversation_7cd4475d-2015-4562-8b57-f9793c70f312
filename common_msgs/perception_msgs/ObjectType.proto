syntax = "proto2";  

package robosense.perception_msgs;

enum ObjectType
{
    TYPE_UNKNOWN = 0;
    TYPE_CAR = 1;
    TYPE_BUS = 2;
    TYPE_TRUCK = 3;
    TYPE_CONSTRN_VEH = 4;
    TYPE_CYC = 5;
    TYPE_TRICYCLE = 6;
    TYPE_PED = 7;
    TYPE_TRAFFIC_CONE = 8;
    TYPE_BARROW = 9;
    TYPE_ANIMAL = 10;
    TYPE_WARN_TRIANGLE = 11;
    TYPE_BIRD = 12;
    TYPE_WATER_BARRIER = 13;
    TYPE_LAMP_POST = 14;
    TYPE_TRAFFIC_SIGN = 15;
    TYPE_WARN_POST = 16;
    TYPE_TRAFFIC_BARREL = 17;
    TYPE_ARTICULATED_HEAD = 18;
    TYPE_ARTICULATED_BODY = 19;
    TYPE_VISION_OBSTACLE = 20;
    TYPE_STRAIGHT_ARM_BARRIER = 21; 
    TYPE_FOLDING_ARM_BARRIER = 22; 
    TYPE_FENCE_ARM_BARRIER = 23; 
    TYPE_STATIC_UNKNOWN = 50;
    TYPE_VIRTUAL = 51;
    TYPE_CURB = 52;
}
