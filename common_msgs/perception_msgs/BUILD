## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "Vec2D_proto",
    srcs = ["Vec2D.proto"],
)

proto_library(
    name = "occupancy_proto",
    srcs = ["occupancy.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "BBox2D_proto",
    srcs = ["BBox2D.proto"],
)

proto_library(
    name = "Size3D_proto",
    srcs = ["Size3D.proto"],
)

proto_library(
    name = "perception_obstacle_proto",
    srcs = ["perception_obstacle.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:error_code_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/map_msgs:map_lane_proto",
    ],
)

proto_library(
    name = "ObjectType_proto",
    srcs = ["ObjectType.proto"],
)

proto_library(
    name = "Vec3D_proto",
    srcs = ["Vec3D.proto"],
)

proto_library(
    name = "TrafficlightTypes_proto",
    srcs = ["TrafficlightTypes.proto"],
)

proto_library(
    name = "perception_camera_proto",
    srcs = ["perception_camera.proto"],
    deps = [
        ":perception_obstacle_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "ObjectFusion_proto",
    srcs = ["ObjectFusion.proto"],
    deps = [
        ":MotionType_proto",
        ":ObjectType_proto",
        ":Size3D_proto",
        ":Vec2D_proto",
        ":Vec3D_proto",
    ],
)

proto_library(
    name = "traffic_light_detection_proto",
    srcs = ["traffic_light_detection.proto"],
    deps = [
        ":TrafficlightTypes_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "perception_lane_proto",
    srcs = ["perception_lane.proto"],
    deps = [
        ":perception_camera_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

proto_library(
    name = "MotionType_proto",
    srcs = ["MotionType.proto"],
)

proto_library(
    name = "road_structure_proto",
    srcs = ["road_structure.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:header_proto",
    ],
)

apollo_package()
